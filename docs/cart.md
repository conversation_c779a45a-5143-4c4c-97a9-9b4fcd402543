以下是 [ShoppingCartController](file://D:\code\Gift\Gift\src\main\java\com\xnlpgyl\gift\Gift\car\controller\ShoppingCartController.java#L20-L163) 的 RESTful API 调用文档，基于其定义的接口和注释。你可以将以下内容保存为 `docs/shopping_cart_api.md` 文件。

---

# 购物车 API 文档

## 1. 获取用户购物车列表

### 请求信息
- **URL**: `/gift/cart/list/{userId}`
- **方法**: `GET`
- **认证**: `@MyAuthCheck(auth = "getUserCart", desc = "获取用户购物车列表")`

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|------|------|------|------|
| userId | long | 是 | 用户ID |

### 响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      // 购物车商品信息
    }
  ]
}
```


---

## 2. 添加商品到购物车

### 请求信息
- **URL**: `/gift/cart/add`
- **方法**: `POST`
- **认证**: `@MyAuthCheck(auth = "addToCart", desc = "添加商品到购物车")`

### 请求体
```json
{
  "userId": 123,
  "goodsId": "goods123",
  "quantity": 2,
  "selected": true,
  "selectedSkuIndex": 0,
  "goodsInfo": {
    // 商品详情
  }
}
```


### 响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 添加后的购物车信息
  }
}
```


---

## 3. 更新购物车商品数量

### 请求信息
- **URL**: `/gift/cart/update`
- **方法**: `PUT`
- **认证**: `@MyAuthCheck(auth = "updateCartItem", desc = "更新购物车商品")`

### 请求体
```json
{
  "_id": "cartItemId123",
  "quantity": 3
}
```


### 响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 更新后的购物车项
  }
}
```


---

## 4. 从购物车中删除商品

### 请求信息
- **URL**: `/gift/cart/remove/{cartItemId}`
- **方法**: `DELETE`
- **认证**: `@MyAuthCheck(auth = "removeFromCart", desc = "从购物车中删除商品")`

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|------|------|------|------|
| cartItemId | String | 是 | 购物车项ID |

### 响应
```json
{
  "code": 200,
  "message": "删除成功"
}
```


---

## 5. 批量删除购物车商品

### 请求信息
- **URL**: `/gift/cart/batchRemove`
- **方法**: `DELETE`
- **认证**: `@MyAuthCheck(auth = "batchRemoveFromCart", desc = "批量删除购物车商品")`

### 请求体
```json
[
  "cartItemId1",
  "cartItemId2"
]
```


### 响应
```json
{
  "code": 200,
  "message": "批量删除成功"
}
```


---

## 6. 更新购物车商品选中状态

### 请求信息
- **URL**: `/gift/cart/select`
- **方法**: `PUT`
- **认证**: `@MyAuthCheck(auth = "updateCartItemSelection", desc = "更新购物车商品选中状态")`

### 请求体
```json
{
  "cartItemId": "cartItemId123",
  "selected": true
}
```


### 响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 更新后的购物车项
  }
}
```


---

## 7. 清空用户购物车

### 请求信息
- **URL**: `/gift/cart/clear/{userId}`
- **方法**: `DELETE`
- **认证**: `@MyAuthCheck(auth = "clearCart", desc = "清空用户购物车")`

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|------|------|------|------|
| userId | long | 是 | 用户ID |

### 响应
```json
{
  "code": 200,
  "message": "购物车已清空"
}
```


---