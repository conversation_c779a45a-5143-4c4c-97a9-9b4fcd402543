# WebSocket 服务模块使用说明

## 概述

本模块提供了完整的 WebSocket 实时通信功能，包括：
- 消息的收发（单发、群发）
- 连接终端管理
- 在线用户管理
- 心跳检测
- 系统通知

## 模块结构

```
com.xnlpgyl.gift.Gift.websocket/
├── config/                 # 配置类
│   └── WebSocketConfig.java
├── controller/             # REST API 控制器
│   └── WebSocketController.java
├── dto/                    # 数据传输对象
│   ├── SendMessageRequest.java
│   └── WebSocketMessage.java
├── enums/                  # 枚举类
│   └── MessageType.java
├── handler/                # WebSocket 处理器
│   └── WebSocketHandler.java
├── interceptor/            # 拦截器
│   └── WebSocketInterceptor.java
├── manager/                # 连接管理器
│   └── WebSocketConnectionManager.java
├── service/                # 业务服务
│   └── WebSocketMessageService.java
└── task/                   # 定时任务
    └── WebSocketCleanupTask.java
```

## 客户端连接

### WebSocket 连接地址
```
ws://localhost:8080/websocket/{userId}
```

### 连接示例（JavaScript）
```javascript
const userId = "user123";
const websocket = new WebSocket(`ws://localhost:8080/websocket/${userId}`);

websocket.onopen = function(event) {
    console.log("WebSocket连接已建立");
};

websocket.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log("收到消息:", message);
};

websocket.onclose = function(event) {
    console.log("WebSocket连接已关闭");
};

websocket.onerror = function(error) {
    console.error("WebSocket错误:", error);
};
```

## 消息格式

### 发送消息格式
```json
{
    "type": "PRIVATE_MESSAGE",
    "toUserId": "user456",
    "content": "Hello World",
    "extra": {}
}
```

### 接收消息格式
```json
{
    "messageId": "uuid-string",
    "type": "PRIVATE_MESSAGE",
    "fromUserId": "user123",
    "toUserId": "user456",
    "content": "Hello World",
    "timestamp": "2025-07-03T10:30:00",
    "extra": {}
}
```

## 消息类型

| 类型 | 代码 | 说明 |
|------|------|------|
| 连接成功 | CONNECT | 连接建立时系统发送 |
| 断开连接 | DISCONNECT | 连接断开时系统发送 |
| 心跳检测 | HEARTBEAT | 客户端发送ping，服务端回复pong |
| 单发消息 | PRIVATE_MESSAGE | 点对点私聊消息 |
| 群发消息 | BROADCAST_MESSAGE | 广播给所有在线用户 |
| 系统通知 | SYSTEM_NOTIFICATION | 系统通知消息 |
| 在线用户列表 | ONLINE_USERS | 在线用户列表 |
| 错误消息 | ERROR | 错误信息 |

## REST API 接口

### 1. 发送消息
```http
POST /websocket/send
Content-Type: application/json

{
    "fromUserId": "user123",
    "toUserId": "user456",
    "content": "Hello World",
    "messageType": "private",
    "extra": {}
}
```

### 2. 发送系统通知
```http
POST /websocket/notification?userId=user123&content=系统维护通知
```

### 3. 获取在线用户列表
```http
GET /websocket/online-users
```

### 4. 检查用户在线状态
```http
GET /websocket/online-status/user123
```

### 5. 获取连接统计信息
```http
GET /websocket/stats
```

### 6. 强制断开用户连接
```http
POST /websocket/disconnect/user123?reason=违规操作
```

## 使用示例

### 客户端发送私聊消息
```javascript
const message = {
    type: "PRIVATE_MESSAGE",
    toUserId: "user456",
    content: "你好，这是一条私聊消息"
};
websocket.send(JSON.stringify(message));
```

### 客户端发送广播消息
```javascript
const message = {
    type: "BROADCAST_MESSAGE",
    content: "大家好，这是一条广播消息"
};
websocket.send(JSON.stringify(message));
```

### 客户端心跳检测
```javascript
const heartbeat = {
    type: "HEARTBEAT",
    content: "ping"
};
websocket.send(JSON.stringify(heartbeat));
```

### 服务端发送消息（Java）
```java
@Autowired
private WebSocketMessageService messageService;

// 发送私聊消息
WebSocketMessage message = WebSocketMessage.createPrivateMessage("user1", "user2", "Hello");
messageService.sendMessageToUser("user2", message);

// 发送广播消息
WebSocketMessage broadcast = WebSocketMessage.createBroadcastMessage("admin", "系统通知");
messageService.broadcastMessage(broadcast);

// 发送系统通知
messageService.sendSystemNotification("user123", "您有新的消息");
```

## 注意事项

1. **用户ID验证**：连接时必须提供有效的用户ID
2. **消息格式**：所有消息必须是有效的JSON格式
3. **连接管理**：系统会自动清理无效连接
4. **心跳检测**：建议客户端定期发送心跳消息保持连接
5. **错误处理**：注意处理连接断开和错误消息
6. **跨域配置**：生产环境建议配置具体的允许域名

## 监控和维护

- 系统每5分钟自动清理无效连接
- 每小时打印连接统计信息到日志
- 可通过REST API获取实时连接状态
- 支持强制断开指定用户连接

## 扩展功能

可以根据需要扩展以下功能：
- 消息持久化存储
- 消息推送到离线用户
- 群组聊天功能
- 消息加密
- 文件传输支持
- 音视频通话信令
