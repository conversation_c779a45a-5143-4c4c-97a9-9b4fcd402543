package com.xnlpgyl.gift.Gift;

import com.xnlpgyl.gift.Gift.utils.AuthInitConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableScheduling
@EnableTransactionManagement
public class GiftApplication {

	public static void main(String[] args) {
		// 启动Spring Boot应用
		// 这里的ConfigurableApplicationContext可以用来获取Bean和其他Spring相关的功能
		// AuthInitConfig.init(configurableApplicationContext); 这行代码会在应用启动后执行, 用于初始化认证配置
		ConfigurableApplicationContext configurableApplicationContext = SpringApplication.run(GiftApplication.class, args);
		AuthInitConfig.init(configurableApplicationContext);
	}
}
