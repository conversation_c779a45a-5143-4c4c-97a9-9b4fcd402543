package com.xnlpgyl.gift.Gift.SysUserRole.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统用户角色关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@TableName("t_sys_user_role")
public class SysUserRole implements Serializable {

    public SysUserRole( Long userId, Long roleId) {
        this.userId = userId;
        this.roleId = roleId;
    }
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户编号
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 角色编号
     */
    @TableField("role_id")
    private Long roleId;

}
