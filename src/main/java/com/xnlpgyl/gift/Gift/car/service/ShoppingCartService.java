package com.xnlpgyl.gift.Gift.car.service;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.car.dao.ShoppingCartDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 购物车服务层
 */
@Service
public class ShoppingCartService {

    @Autowired
    private ShoppingCartDao shoppingCartDao;

    /**
     * 获取用户购物车列表
     */
    public List<JSONObject> getUserCart(long userId) {
        return shoppingCartDao.getCartByUserId(userId);
    }

    /**
     * 添加商品到购物车
     * 如果商品已存在，则增加数量
     */
    public JSONObject addToCart(JSONObject cartRequest) {
        long userId = cartRequest.getLongValue("userId");
        String goodsId = cartRequest.getString("goodsId");
        int selectedSkuIndex = cartRequest.getIntValue("selectedSkuIndex", 0);
        int quantity = cartRequest.getIntValue("quantity", 1);

        // 检查商品是否已在购物车中
        JSONObject existingItem = shoppingCartDao.findCartItem(userId, goodsId, selectedSkuIndex);

        if (existingItem != null) {
            // 商品已存在，增加数量
            int currentQuantity = existingItem.getIntValue("quantity", 0);
            existingItem.put("quantity", currentQuantity + quantity);
            existingItem.put("updateTime", new Date());
            return shoppingCartDao.updateCartItemQuantity(existingItem);
        } else {
            // 商品不存在，添加新商品
            JSONObject newCartItem = new JSONObject();
            newCartItem.put("_id", UUID.randomUUID().toString());
            newCartItem.put("userId", userId);
            newCartItem.put("goodsId", goodsId);
            newCartItem.put("quantity", quantity);
            newCartItem.put("selectedSkuIndex", selectedSkuIndex);
            newCartItem.put("selected", true);
            newCartItem.put("createTime", new Date());
            newCartItem.put("updateTime", new Date());

            // 复制商品信息
            if (cartRequest.containsKey("goodsInfo")) {
                newCartItem.put("goodsInfo", cartRequest.get("goodsInfo"));
            }

            return shoppingCartDao.addToCart(newCartItem);
        }
    }

    /**
     * 更新购物车商品数量
     */
    public JSONObject updateCartItemQuantity(JSONObject cartItem) {
        Object cartItemId = cartItem.remove("_id");
        if (cartItemId == null ) {
            throw new IllegalArgumentException("购物车项ID不能为空");
        }
        String cartItemIdStr = cartItemId.toString();
        // 设置更新时间
        cartItem.put("updateTime", new Date());

        return shoppingCartDao.updateCartItem(cartItemIdStr,cartItem);
    }

    /**
     * 从购物车中删除商品
     */
    public boolean removeFromCart(String cartItemId) {
        return shoppingCartDao.removeFromCart(cartItemId);
    }

    /**
         * 批量删除购物车商品
         *
         * @param cartItemIds 购物车项ID列表
         */
        public void batchRemoveFromCart(List<String> cartItemIds) {
            for (String cartItemId : cartItemIds) {
                removeFromCart(cartItemId);
            }
        }

        /**
     * 更新购物车商品选中状态
     */
    public JSONObject updateCartItemSelection(String cartItemId, boolean selected) {
        JSONObject cartItem = new JSONObject();
        cartItem.put("_id", cartItemId);
        cartItem.put("selected", selected);
        cartItem.put("updateTime", new Date());

        return shoppingCartDao.updateCartItemQuantity(cartItem);
    }

    /**
     * 清空用户购物车
     */
    public void clearCart(long userId) {
        shoppingCartDao.clearCart(userId);
    }

    /**
     * 更新购物车商品
     */
    public JSONObject updateCartItem(JSONObject cartItem) {
        String _id = cartItem.remove("_id").toString();
        return shoppingCartDao.updateCartItem(_id,cartItem);
    }
}
