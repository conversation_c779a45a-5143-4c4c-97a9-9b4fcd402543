package com.xnlpgyl.gift.Gift.car.dao;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.BaseDao;
import com.xnlpgyl.gift.Gift.config.MongoDbColName;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 购物车数据访问层
 */
@Repository
public class ShoppingCartDao extends BaseDao {

    /**
     * 根据用户ID获取购物车列表
     */
    public List<JSONObject> getCartByUserId(long userId) {
        Query query = new Query(Criteria.where("userId").is(userId));
        return find(query, MongoDbColName.SHOPPING_CART);
    }

    /**
     * 添加商品到购物车
     */
    public JSONObject addToCart(JSONObject cartItem) {
        return saveOrUpdate(cartItem, MongoDbColName.SHOPPING_CART);
    }

    /**
     * 更新购物车商品数量
     */
    public JSONObject updateCartItemQuantity(JSONObject cartItem) {
        return saveOrUpdate(cartItem, MongoDbColName.SHOPPING_CART);
    }

    /**
     * 从购物车中删除商品
     */
    public boolean removeFromCart(String cartItemId) {
        return deleteById(cartItemId, MongoDbColName.SHOPPING_CART);
    }

    /**
     * 检查商品是否已在购物车中
     */
    public JSONObject findCartItem(long userId, String goodsId, int selectedSkuIndex) {
        Query query = new Query(Criteria.where("userId").is(userId)
                .and("goodsId").is(goodsId).and("selectedSkuIndex").is(selectedSkuIndex));
        return find(query, MongoDbColName.SHOPPING_CART).stream().findFirst().orElse(null);
    }

    /**
     * 清空用户购物车
     */
    public void clearCart(long userId) {
        Query query = new Query(Criteria.where("userId").is(userId));
        delete(query, MongoDbColName.SHOPPING_CART);
    }

    /**
     * 批量删除购物车商品
     *
     * @param cartItemIds 购物车项ID列表
     */
    public void batchRemoveFromCart(List<String> cartItemIds) {
        for (String cartItemId : cartItemIds) {
            removeFromCart(cartItemId);
        }
    }
    /**
     * 更新购物车项信息
     */
    public JSONObject updateCartItem(String _id,JSONObject cartItem) {
        return updateFieldsById(_id,cartItem, MongoDbColName.SHOPPING_CART);
    }
}
