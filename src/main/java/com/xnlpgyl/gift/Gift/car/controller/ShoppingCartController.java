package com.xnlpgyl.gift.Gift.car.controller;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.car.service.ShoppingCartService;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.config.BusinessException;
import com.xnlpgyl.gift.Gift.security.MyAuthCheck;
import com.xnlpgyl.gift.Gift.security.MyMenuCheck;
import com.xnlpgyl.gift.Gift.utils.ResultCode;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 购物车控制器
 */
@RestController
@RequestMapping("/gift/cart")
@MyMenuCheck(auth = "gift:cart:", desc = "购物车管理")
public class ShoppingCartController extends BaseController {

    @Autowired
    private ShoppingCartService shoppingCartService;

    public ShoppingCartController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }

    /**
     * 获取用户购物车列表
     *
     * @param userId 用户ID
     * @return 购物车列表
     */
    @GetMapping("/list/{userId}")
    @MyAuthCheck(auth = "getUserCart", desc = "获取用户购物车列表")
    public ResultMsg<List<JSONObject>> getUserCart(@PathVariable long userId) {
        List<JSONObject> cartItems = shoppingCartService.getUserCart(userId);
        return ResultMsg.success(cartItems);
    }

    /**
     * 添加商品到购物车
     *
     * @param cartRequest 购物车请求（包含userId, goodsId, quantity等）
     * @return 添加结果
     */
    @PostMapping("/add")
    @MyAuthCheck(auth = "addToCart", desc = "添加商品到购物车")
    public ResultMsg<JSONObject> addToCart(@RequestBody JSONObject cartRequest) {
        if (cartRequest.getLongValue("userId") == 0 || cartRequest.getString("goodsId") == null) {
            throw new BusinessException(ResultCode.PARAM_NOT_COMPLETE);
        }

        JSONObject result = shoppingCartService.addToCart(cartRequest);
        if (result == null) {
            throw new BusinessException(ResultCode.ERROR);
        }
        return ResultMsg.success(result);
    }

    /**
     * 更新购物车商品数量
     *
     * @param cartItem 购物车项（包含_id和quantity）
     * @return 更新结果
     */
    @PutMapping("/update")
    @MyAuthCheck(auth = "updateCartItem", desc = "更新购物车商品")
    public ResultMsg<JSONObject> updateCartItem(@RequestBody JSONObject cartItem) {
        if (cartItem.getString("_id") == null) {
            throw new BusinessException(ResultCode.PARAM_NOT_COMPLETE);
        }

        JSONObject result = shoppingCartService.updateCartItemQuantity(cartItem);
        if (result == null) {
            throw new BusinessException(ResultCode.ERROR);
        }
        return ResultMsg.success(result);
    }

    /**
     * 从购物车中删除商品
     *
     * @param cartItemId 购物车项ID
     * @return 删除结果
     */
    @DeleteMapping("/remove/{cartItemId}")
    @MyAuthCheck(auth = "removeFromCart", desc = "从购物车中删除商品")
    public ResultMsg<String> removeFromCart(@PathVariable String cartItemId) {
        boolean success = shoppingCartService.removeFromCart(cartItemId);
        if (!success) {
            throw new BusinessException(ResultCode.ERROR);
        }
        return ResultMsg.success("删除成功");
    }

    /**
     * 批量删除购物车商品
     *
     * @param cartItemIds 购物车项ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batchRemove")
    @MyAuthCheck(auth = "batchRemoveFromCart", desc = "批量删除购物车商品")
    public ResultMsg<String> batchRemoveFromCart(@RequestBody List<String> cartItemIds) {
        boolean allSuccess = true;
        for (String cartItemId : cartItemIds) {
            boolean success = shoppingCartService.removeFromCart(cartItemId);
            if (!success) {
                allSuccess = false;
                break;
            }
        }
        if (allSuccess) {
            return ResultMsg.success("批量删除成功");
        } else {
            throw new BusinessException(ResultCode.ERROR);
        }
    }

    

    /**
     * 更新购物车商品选中状态
     *
     * @param params 包含cartItemId和selected的JSON对象
     * @return 更新结果
     */
    @PutMapping("/select")
    @MyAuthCheck(auth = "updateCartItemSelection", desc = "更新购物车商品选中状态")
    public ResultMsg<JSONObject> updateCartItemSelection(@RequestBody JSONObject params) {
        String cartItemId = params.getString("cartItemId");
        Boolean selected = params.getBoolean("selected");

        if (cartItemId == null || selected == null) {
            throw new BusinessException(ResultCode.PARAM_NOT_COMPLETE);
        }

        JSONObject result = shoppingCartService.updateCartItemSelection(cartItemId, selected);
        if (result == null) {
            throw new BusinessException(ResultCode.ERROR);
        }
        return ResultMsg.success(result);
    }

    /**
     * 清空用户购物车
     *
     * @param userId 用户ID
     * @return 清空结果
     */
    @DeleteMapping("/clear/{userId}")
    @MyAuthCheck(auth = "clearCart", desc = "清空用户购物车")
    public ResultMsg<String> clearCart(@PathVariable long userId) {
        shoppingCartService.clearCart(userId);
        return ResultMsg.success("购物车已清空");
    }

}
