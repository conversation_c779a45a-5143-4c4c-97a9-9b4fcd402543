package com.xnlpgyl.gift.Gift.utils;

import java.util.Random;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import javax.imageio.ImageIO;

/**
 * 数学验证码验证器
 */
public class MathCaptchaValidator {
    /**
     * 生成随机十以内的加减法验证问题
     * @return 返回包含数学表达式和正确答案的字符串数组
     * 第一个元素是数学表达式，如 "5 + 3 = "
     * 第二个元素是正确答案
     */
    public static String[] generateMathCaptcha() {
        Random random = new Random();
        
        // 生成两个1-10之间的随机数
        int num1 = random.nextInt(10) + 1;
        int num2 = random.nextInt(10) + 1;
        
        // 随机选择加法或减法
        boolean isAddition = random.nextBoolean();
        
        String operator = isAddition ? "+" : "-";
        int result = isAddition ? num1 + num2 : num1 - num2;
        //如果小于0 则重新生成
        while (result < 0) {
            num1 = random.nextInt(10) + 1;
            num2 = random.nextInt(10) + 1;
            result = num1 - num2;
        }
        // 构建数学表达式
        String expression = num1 + " " + operator + " " + num2 + " = ";
        
        return new String[] {expression, String.valueOf(result)};
    }
    
    /**
     * 生成包含数学表达式的验证码图片
     * @param expression 数学表达式，如 "5 + 3 = "
     * @return 返回包含数学表达式验证码的BufferedImage对象
     */
    public static BufferedImage generateCaptchaImage(String expression) {
        int width = 200;
        int height = 80;
        
        // 创建空白图片
        BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = bufferedImage.createGraphics();
        
        // 设置背景色
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        
        // 设置字体
        Font font = new Font("Arial", Font.BOLD, 36);
        g2d.setFont(font);
        g2d.setColor(Color.BLACK);
        
        // 绘制数学表达式
        int x = 20;
        int y = 50;
        g2d.drawString(expression, x, y);


        // 添加干扰线
        Random random = new Random();
        for (int i = 0; i < 15; i++) {
            g2d.setColor(new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256)));
            int x1 = random.nextInt(width);
            int y1 = random.nextInt(height);
            int x2 = random.nextInt(width);
            int y2 = random.nextInt(height);
            g2d.drawLine(x1, y1, x2, y2);
        }
        g2d.setColor(Color.BLACK);
        g2d.dispose();
        return bufferedImage;
    }
}