package com.xnlpgyl.gift.Gift.utils;

import cn.hutool.core.util.RandomUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.io.FileOutputStream;
import java.util.Base64;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class Base64ImageExtractor {
    @Value("${file.upload.path}")
    private String uploadPath;
    public String extractBase64Image(String imgBase64Str) {
        String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String fileName="";
        try {
            // Step 1: 使用正则表达式提取Base64数据
            Pattern pattern = Pattern.compile(
                    "data:image/(?<mime>[\\w/]+);base64,(?<data>[^\"]+)",
                    Pattern.CASE_INSENSITIVE
            );
            //匹配多个图片
            Matcher matcher = pattern.matcher(imgBase64Str);
            if (matcher.find()) {
                // Step 2: 获取MIME类型和Base64数据
                String mimeType = matcher.group("mime");
                String base64Data = matcher.group("data");
                // Step 3: 解码Base64
                byte[] imageBytes = Base64.getDecoder().decode(base64Data);

                String newFilePath = uploadPath + date + File.separator;
                File fileDir = new File(newFilePath);
                if (!fileDir.exists()) {
                    fileDir.mkdirs();
                }
                // Step 4: 保存文件（根据MIME类型生成扩展名）
                fileName = System.currentTimeMillis()+ "_" +RandomUtil.randomNumbers(6) +"." + mimeType;
                String outputPath = newFilePath+fileName;
                try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                    fos.write(imageBytes);
                    fos.flush();
                    System.out.println("文件保存成功: " + outputPath);
                }
            } else {
                System.out.println("未找到Base64图片数据");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Base64.getEncoder().encodeToString((date + File.separator + fileName).getBytes(StandardCharsets.UTF_8));
    }
}
