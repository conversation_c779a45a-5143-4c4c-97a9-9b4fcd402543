package com.xnlpgyl.gift.Gift.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xnlpgyl.gift.Gift.SysAuthorization.entity.SysAuthorization;
import com.xnlpgyl.gift.Gift.SysAuthorization.service.ISysAuthorizationService;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.config.ObjectIdConfig;
import com.xnlpgyl.gift.Gift.security.MyAuthCheck;
import com.xnlpgyl.gift.Gift.security.MyMenuCheck;
import org.springframework.aop.support.AopUtils;
import org.springframework.context.ConfigurableApplicationContext;

import java.lang.reflect.Method;
import java.util.*;

public class AuthInitConfig {
    // 全部权限路径
    public static List<SysAuthorization> authPath = new ArrayList<>();
    public static JSONArray menus = new JSONArray();
    // 初始化权限路径
    public static JSONArray init(ConfigurableApplicationContext configurableApplicationContext) {
        // 如果传入的上下文为空，则直接返回菜单
        if (configurableApplicationContext == null) {
            return menus;
        }
        // 配置ObjectId
        ObjectIdConfig.configure();
        // 获取所有BaseController类型的bean
        Map<String, BaseController> baseControllerMap = configurableApplicationContext.getBeansOfType(BaseController.class);
        // 遍历所有BaseController类型的bean
        for (Map.Entry<String, BaseController> entry : baseControllerMap.entrySet()) {
            BaseController controller = entry.getValue();
            // 如果bean为空，则跳过
            if (controller == null) {
                continue;
            }
            // 获取bean的实际类型
            Class<?> controllerClass = AopUtils.getTargetClass(controller);
            // 获取MyMenuCheck注解
            MyMenuCheck menuCheck = controllerClass.getAnnotation(MyMenuCheck.class);
            // 如果存在MyMenuCheck注解
            if (menuCheck != null) {
                // 获取权限路径和描述
                String myAuthBasePath = menuCheck.auth();
                String myAuthDescription = menuCheck.desc();
                // 添加权限路径
                authPath.add(new SysAuthorization(myAuthBasePath, myAuthDescription, "/", true));
                // 获取所有方法
                Method[] methods = controllerClass.getDeclaredMethods();
                // 遍历所有方法
                for (Method method : methods) {
                    // 获取MyAuthCheck注解
                    MyAuthCheck myAuthCheckMethod = method.getAnnotation(MyAuthCheck.class);
                    // 如果存在MyAuthCheck注解
                    if (myAuthCheckMethod != null) {
                        // 获取权限路径和描述
                        String myAuthCheckAuth =myAuthBasePath + myAuthCheckMethod.auth();
                        String methodDescription = myAuthCheckMethod.desc();
                        // 添加权限路径
                        authPath.add(new SysAuthorization(myAuthCheckAuth, methodDescription, myAuthBasePath, false));
                    }
                }
            }
        }
        //跟新数据
        ISysAuthorizationService sysAuthorizationService = configurableApplicationContext.getBean(ISysAuthorizationService.class);
        List<SysAuthorization> sysAuthorizationList = sysAuthorizationService.list();
        if(sysAuthorizationList.isEmpty()){
            sysAuthorizationService.saveBatch(authPath);
        }else {
            for (SysAuthorization sysAuthorization : authPath) {
                SysAuthorization sysAuthorization1 = sysAuthorizationService.getOne(new QueryWrapper<SysAuthorization>().eq("auth_path", sysAuthorization.getAuthPath()));
                if(sysAuthorization1 == null){
                    sysAuthorizationService.save(sysAuthorization);
                }
            }
        }
        for (SysAuthorization sysAuthorization : sysAuthorizationList) {
            String dBaseAuthPath = sysAuthorization.getAuthPath();
            boolean isExist = false;
            for (SysAuthorization sysAuthorization1 : authPath){
                String dAuthPath = sysAuthorization1.getAuthPath();
                isExist = dBaseAuthPath.equals(dAuthPath);
                if (isExist){
                    break;
                }
            }
            if(!isExist){
                // 删除
                sysAuthorizationService.removeById(sysAuthorization.getId());
            }

        }
        LambdaQueryWrapper<SysAuthorization> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(SysAuthorization::getAuthPath);
        authPath = sysAuthorizationService.list(queryWrapper);
        Map<String,SysAuthorization> m = new HashMap<>();
        for (SysAuthorization sysAuthorization : authPath) {
            if(sysAuthorization.getIsMenuAuth()==1){
                if (!m.containsKey(sysAuthorization.getAuthPath())) {
                    m.put(sysAuthorization.getAuthPath(), sysAuthorization);
                    JSONObject nodeM = JSONObject.from(sysAuthorization);
                    JSONArray children = new JSONArray();
                    for (SysAuthorization sysAuthorizationX : authPath) {
                        if(sysAuthorizationX.getIsMenuAuth()==0&&sysAuthorization.getAuthPath().equals(sysAuthorizationX.getParentAuthPath())) {
                            children.add(sysAuthorizationX);
                        }
                    }
                    nodeM.put("children",children);
                    menus.add(nodeM);
                }
            }
        }
        return menus;
    }
}
