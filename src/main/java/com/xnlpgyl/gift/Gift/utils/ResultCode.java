package com.xnlpgyl.gift.Gift.utils;

/**
 * API 响应状态码枚举
 */
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误 4xx
    ERROR(400, "操作失败"),
    UNAUTHORIZED(401, "未授权"),
    TOKEN_ERROR(402, "令牌数据错误"),
    TOKEN_INVALID(410, "令牌数据无效"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "方法不允许"),
    PARAM_ERROR(406, "参数错误"),
    REQUEST_TIMEOUT(408, "请求超时"),
    CONFLICT(409, "资源冲突"),

    // 服务端错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    // 业务错误码 1000~9999
    USER_NOT_EXIST(1001, "用户不存在"),
    USER_PASSWORD_ERROR(1002, "用户名或密码错误"),
    USER_ACCOUNT_LOCKED(1003, "账号已被锁定"),
    USER_ACCOUNT_INVALID(1004, "账号无效"),

    // 商品相关错误码
    GOODS_NOT_EXIST(2001, "商品不存在"),
    GOODS_STOCK_NOT_ENOUGH(2002, "商品库存不足"),
    GOODS_EXIST(2003, "商品已存在"),
    // 订单相关错误码
    ORDER_NOT_EXIST(3001, "订单不存在"),
    ORDER_ALREADY_PAID(3002, "订单已支付"),
    ORDER_ALREADY_CANCELED(3003, "订单已取消"),

    // 支付相关错误码
    PAYMENT_FAILED(4001, "支付失败"),

    // 系统错误
    SYSTEM_ERROR(9001, "系统错误"),
    UNKNOWN_ERROR(9999, "未知错误"),
    FILE_TOO_LARGE(9998, "文件过大"),
    FILE_NOT_EXIST(9997, "文件不存在"),
    INVALID_FILE_TYPE(9996, "文件类型错误"),
    FILE_UPLOAD_FAILED(9995, "文件上传失败"),
    FILE_NOT_FOUND(9994, "文件未找到"),
    FILE_SAVE_ERROR(9993, "文件保存失败"), PARAM_NOT_COMPLETE(9992, "参数不完整");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}