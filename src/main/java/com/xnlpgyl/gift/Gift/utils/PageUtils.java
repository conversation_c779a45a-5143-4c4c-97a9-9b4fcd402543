package com.xnlpgyl.gift.Gift.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class PageUtils {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将 MyBatis-Plus 的 Page 对象转换为标准格式的 JSON 字符串
     *
     * @param page MyBatis-Plus 的分页对象
     * @return 包含分页信息的 JSON 字符串
     */
    public static String toJsonString(Page<?> page) {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("records", page.getRecords());
            result.put("total", page.getTotal());
            result.put("size", page.getSize());
            result.put("current", page.getCurrent());
            result.put("pages", page.getPages());
            result.put("hasNext", page.hasNext());
            result.put("hasPrevious", page.hasPrevious());

            return objectMapper.writeValueAsString(result);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert Page to JSON", e);
            return "{\"error\": \"Failed to convert Page to JSON\"}";
        }
    }

    /**
     * 将 MyBatis-Plus 的 Page 对象转换为简化的 JSON 字符串（只包含记录和基本分页信息）
     *
     * @param page MyBatis-Plus 的分页对象
     * @return 简化的分页信息 JSON 字符串
     */
    public static String toSimpleJsonString(Page<?> page) {
        try {
            Map<String, Object> result = pageToMap(page);
            return objectMapper.writeValueAsString(result);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert Page to simple JSON", e);
            return "{\"error\": \"Failed to convert Page to simple JSON\"}";
        }
    }

    /**
     * 将 MyBatis-Plus 的 Page 对象转换为 Map 对象
     *
     * @param page MyBatis-Plus 的分页对象
     * @return 包含分页信息的 Map 对象
     */
    public static Map<String, Object> pageToMap(Page<?> page) {
        Map<String, Object> result = new HashMap<>();
        result.put("records", page.getRecords());
        result.put("total", page.getTotal());
        result.put("pageNum", page.getCurrent());
        result.put("pageSize", page.getSize());
        result.put("pages", page.getPages());
        result.put("hasNext", page.hasNext());
        result.put("hasPrevious", page.hasPrevious());
        return result;
    }

    /**
     * 将 MyBatis-Plus 的 Page 对象转换为简化的 Map 对象
     *
     * @param page MyBatis-Plus 的分页对象
     * @return 简化的分页信息 Map 对象
     */
    public static Map<String, Object> pageToSimpleMap(Page<?> page) {
        Map<String, Object> result = new HashMap<>();
        result.put("records", page.getRecords());
        result.put("total", page.getTotal());
        result.put("pageNum", page.getCurrent());
        result.put("pageSize", page.getSize());
        return result;
    }
}