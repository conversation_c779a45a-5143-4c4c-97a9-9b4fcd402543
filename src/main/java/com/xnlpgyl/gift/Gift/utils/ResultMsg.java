package com.xnlpgyl.gift.Gift.utils;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * RESTful API 统一响应结果类
 *
 * @param <T> 响应数据类型
 */
@Data
@NoArgsConstructor
public class ResultMsg<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 私有构造方法
     */
    private ResultMsg(Integer code, String msg, T data, boolean success) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.success = success;
    }

    /**
     * 成功响应（无数据返回）
     *
     * @return ResultMsg对象
     */
    public static <T> ResultMsg<T> success() {
        return new ResultMsg<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), null, true);
    }

    /**
     * 成功响应（自定义消息，无数据）
     *
     * @param message 自定义消息
     * @return ResultMsg对象
     */
    public static <T> ResultMsg<T> success(String message) {
        return new ResultMsg<>(ResultCode.SUCCESS.getCode(), message, null, true);
    }

    /**
     * 成功响应（有数据返回）
     *
     * @param data 返回数据
     * @return ResultMsg对象
     */
    public static <T> ResultMsg<T> success(T data) {
        return new ResultMsg<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data, true);
    }

    /**
     * 成功响应（自定义消息和数据）
     *
     * @param message 自定义消息
     * @param data    返回数据
     * @return ResultMsg对象
     */
    public static <T> ResultMsg<T> success(String message, T data) {
        return new ResultMsg<>(ResultCode.SUCCESS.getCode(), message, data, true);
    }

    /**
     * 失败响应（使用默认错误码和消息）
     *
     * @return ResultMsg对象
     */
    public static <T> ResultMsg<T> error() {
        return new ResultMsg<>(ResultCode.ERROR.getCode(), ResultCode.ERROR.getMessage(), null, false);
    }

    /**
     * 失败响应（自定义消息，使用默认错误码）
     *
     * @param message 错误消息
     * @return ResultMsg对象
     */
    public static <T> ResultMsg<T> error(String message) {
        return new ResultMsg<>(ResultCode.ERROR.getCode(), message, null, false);
    }

    /**
     * 失败响应（自定义错误码和消息）
     *
     * @param code    错误码
     * @param message 错误消息
     * @return ResultMsg对象
     */
    public static <T> ResultMsg<T> error(Integer code, String message) {
        return new ResultMsg<>(code, message, null, false);
    }

    /**
     * 失败响应（使用预定义的结果码）
     *
     * @param resultCode 结果码枚举
     * @return ResultMsg对象
     */
    public static <T> ResultMsg<T> error(ResultCode resultCode) {
        return new ResultMsg<>(resultCode.getCode(), resultCode.getMessage(), null, false);
    }

    /**
     * 失败响应（使用预定义的结果码和自定义数据）
     *
     * @param resultCode 结果码枚举
     * @param data       错误数据
     * @return ResultMsg对象
     */
    public static <T> ResultMsg<T> error(ResultCode resultCode, T data) {
        return new ResultMsg<>(resultCode.getCode(), resultCode.getMessage(), data, false);
    }

    /**
     * 自定义响应
     *
     * @param code    状态码
     * @param message 响应消息
     * @param data    响应数据
     * @param success 是否成功
     * @return ResultMsg对象
     */
    public static <T> ResultMsg<T> custom(Integer code, String message, T data, boolean success) {
        return new ResultMsg<>(code, message, data, success);
    }
}
