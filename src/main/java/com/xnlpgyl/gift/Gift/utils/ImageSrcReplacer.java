package com.xnlpgyl.gift.Gift.utils;

import cn.hutool.core.util.RandomUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ImageSrcReplacer {
    @Autowired
    private Base64ImageExtractor base64ImageExtractor;
    @Value("${file.download.url}")
    private String downloadUrl;

    public  String replaceImageSrc(String html) {
        // 原始HTML内容
        int idx = html.indexOf("<img src=\"data:image/");
        while (idx != -1) {
            int endIdx = html.indexOf(">", idx);
            if (endIdx != -1) {
                String imgTag = html.substring(idx, endIdx + 1);
                // 替换后的目标URL
                String replacement ="<img style=\"width:100%;height:auto;\" src="+ downloadUrl+base64ImageExtractor.extractBase64Image(imgTag)+">";
                // 执行替换
                html = html.replace(imgTag, replacement);
                idx = html.indexOf("<img src=\"data:image/");
            }
        }
        System.out.println(html);
        return html;
    }
}
