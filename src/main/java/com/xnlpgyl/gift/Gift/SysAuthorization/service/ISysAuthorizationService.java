package com.xnlpgyl.gift.Gift.SysAuthorization.service;

import com.xnlpgyl.gift.Gift.SysAuthorization.entity.SysAuthorization;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Set;

/**
 * <p>
 * 系统用户权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-30
 */
public interface ISysAuthorizationService extends IService<SysAuthorization> {

    Set<String> getAuthoritiesByRoleId(Long id);
}
