package com.xnlpgyl.gift.Gift.SysAuthorization.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xnlpgyl.gift.Gift.SysAuthorization.entity.SysAuthorization;

import java.util.Set;

/**
 * <p>
 * 系统用户权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-30
 */
public interface SysAuthorizationMapper extends BaseMapper<SysAuthorization> {
    Set<SysAuthorization> getAuthoritiesByRoleId(Long id);
}
