package com.xnlpgyl.gift.Gift.SysAuthorization.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xnlpgyl.gift.Gift.config.BasePojo;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统用户权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-30
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@TableName("t_sys_authorization")
public class SysAuthorization extends BasePojo implements Serializable {
    public SysAuthorization(String authPath,String authDesc,String parentAuthPath,boolean isMenuAuth){
        this.authPath = authPath;
        this.authDesc = authDesc;
        this.parentAuthPath = parentAuthPath;
        this.isMenuAuth = isMenuAuth?(byte)1:(byte)0;
    }

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 权限路径
     */
    @TableField("auth_path")
    private String authPath;

    /**
     * 权限描述
     */
    @TableField("auth_desc")
    private String authDesc;

    /**
     * 是否是菜单权限
     */
    @TableField("is_menu_auth")
    private Byte isMenuAuth;

    /**
     * 父权限路径
     */
    @TableField("parent_auth_path")
    private String parentAuthPath;

}
