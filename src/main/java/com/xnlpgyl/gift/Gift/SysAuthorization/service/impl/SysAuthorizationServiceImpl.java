package com.xnlpgyl.gift.Gift.SysAuthorization.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.SysAuthorization.entity.SysAuthorization;
import com.xnlpgyl.gift.Gift.SysAuthorization.mapper.SysAuthorizationMapper;
import com.xnlpgyl.gift.Gift.SysAuthorization.service.ISysAuthorizationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 系统用户权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-30
 */
@Service
public class SysAuthorizationServiceImpl extends ServiceImpl<SysAuthorizationMapper, SysAuthorization> implements ISysAuthorizationService {

    @Autowired
    private SysAuthorizationMapper sysAuthorizationMapper;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Override
    public Set<String> getAuthoritiesByRoleId(Long id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("roleId").is(id));
        JSONObject apis = mongoTemplate.findOne(query, JSONObject.class, "ApiList");
        if (apis != null) {
            return new HashSet<>(apis.getList("apis", String.class));
        }else {
            return new HashSet<>();
        }
    }
}
