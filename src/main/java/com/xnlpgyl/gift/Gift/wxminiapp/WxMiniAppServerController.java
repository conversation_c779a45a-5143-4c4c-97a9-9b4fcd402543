package com.xnlpgyl.gift.Gift.wxminiapp;

import com.xnlpgyl.gift.Gift.User.service.impl.UserServiceImpl;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.security.MyMenuCheck;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/wx/miniApp")
@MyMenuCheck(auth = "wx:miniApp:",desc = "微信小程序自定义云服务")
public class WxMiniAppServerController extends BaseController {
    @Autowired
    private WxMiniAppServerService wxMiniAppServerService;
    @Autowired
    private UserServiceImpl userService;

    public WxMiniAppServerController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }

    /**
     * 微信小程序 登录 获取 openid
     *
     * @param code 登录凭证
     * @return 登录结果
     */
    @GetMapping("/getOpenId/{code}")
    public ResultMsg<Map<String,Object>> getOpenIdByCode(@PathVariable String code) {
        return ResultMsg.success(wxMiniAppServerService.getSessionInfo(code));
    }

    /**
     * 微信小程序 解密手机号
     *
     * @param code 手机号加密信息
     * @return 手机号信息
     */
    @GetMapping("/getTelephoneNumber/{code}")
    public ResultMsg<Map<String,Object>> getTelephoneNumber(@PathVariable String code) {
        return ResultMsg.success(wxMiniAppServerService.getPhoneNumber(code));
    }
    /**
     * 获取微信小程序用户信息
     */
    @GetMapping("/getTelephoneNumberByOpenId/{openid}")
    public ResultMsg<Map<String,Object>> getTelephoneNumberByOpenId(@PathVariable String openid) {
        return ResultMsg.success(wxMiniAppServerService.getTelephoneNumberByOpenId(openid));
    }
}
