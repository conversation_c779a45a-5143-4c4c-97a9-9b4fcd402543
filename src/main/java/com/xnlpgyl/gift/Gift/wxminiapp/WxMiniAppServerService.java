package com.xnlpgyl.gift.Gift.wxminiapp;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;

@Service
public class WxMiniAppServerService {

    private static final String JSCODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session";
    private static final String JSCODE2TEL_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber";
    private final RestTemplate restTemplate;
    @Autowired
    private OkHttpService okHttpService;

    @Autowired
    private WxMiniAppServerDao  wxMiniAppServerDao;

    @Autowired
    public WxMiniAppServerService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }


    /**
     * 通过微信登录凭证获取用户会话信息
     * @param jsCode 用户登录凭证
     * @return 包含session_key和openid的Map
     */
    public Map<String, Object> getSessionInfo( String jsCode) {
        try {
            // 构建请求URL和参数
            UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(JSCODE2SESSION_URL)
                    .queryParam("appid", WxAccessTokenUtil.APP_ID)
                    .queryParam("secret", WxAccessTokenUtil.SECRET)
                    .queryParam("js_code", jsCode)
                    .queryParam("grant_type", "authorization_code");

            // 发送GET请求，指定期望接收JSON格式的响应
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(List.of(MediaType.APPLICATION_JSON));

            HttpEntity<?> entity = new HttpEntity<>(headers);

            // 使用exchange方法可以更灵活地处理请求和响应
            ResponseEntity<String> response = restTemplate.exchange(
                    builder.toUriString(),
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            // 检查响应状态
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                // 手动解析JSON字符串为Map
                JSONObject result = JSONObject.parseObject(responseBody);
                // 检查是否有错误
                if (result != null && result.containsKey("errcode") && result.get("errcode") != null) {
                    int errcode = Integer.parseInt(result.get("errcode").toString());
                    if (errcode != 0) {
                        String errmsg = result.get("errmsg").toString();
                        System.err.println("微信API调用失败: " + errcode + " - " + errmsg);
                        return null;
                    }
                }

                return result;
            } else {
                System.err.println("HTTP请求失败，状态码: " + response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            System.err.println("调用微信API时发生异常: " + e.getMessage());
            return null;
        }
    }
    /**
     * 根据code获取用户电话号码
     */
    public JSONObject getPhoneNumber(String code) {
        try {
            // 构建请求URL和参数
            String token = WxAccessTokenUtil.getAccessToken(restTemplate);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", code);
            String string = okHttpService.postJson(JSCODE2TEL_URL + "?access_token=" + token, jsonObject.toJSONString());
            return JSONObject.parseObject(string);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public Map<String, Object> getTelephoneNumberByOpenId(String openid) {
        return wxMiniAppServerDao.getByField("password",openid,WxMiniAppServerDao.COLLECTION_NAME_USER);
    }
}
