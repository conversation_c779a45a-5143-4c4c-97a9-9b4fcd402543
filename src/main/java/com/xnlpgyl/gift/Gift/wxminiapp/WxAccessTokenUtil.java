package com.xnlpgyl.gift.Gift.wxminiapp;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

public class WxAccessTokenUtil {
    public static final String APP_ID = "wxae95026f0450c13e";
    public static final String SECRET = "d09c8a47f3dd366f13452e23735337d8";
    public static String AccessToken = "";
    public static long  AccessTokenExpireTime = 0;
    public static final String AccessTokenUrl = "https://api.weixin.qq.com/cgi-bin/token";

    public static String getAccessToken(RestTemplate restTemplate) {
        if (AccessTokenExpireTime < System.currentTimeMillis()) {
            JSONObject result = getToken(restTemplate);
            if (result != null) {
                AccessToken = result.getString( "access_token");
                AccessTokenExpireTime = System.currentTimeMillis() + (result.getIntValue("expires_in") - 200) * 1000L;
            }

        }
        return AccessToken;
    }

    private static JSONObject getToken(RestTemplate restTemplate) {
        try {
            // 构建请求URL和参数
            UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(AccessTokenUrl)
                    .queryParam("appid", WxAccessTokenUtil.APP_ID)
                    .queryParam("secret", WxAccessTokenUtil.SECRET)
                    .queryParam("grant_type", "client_credential");
            // 发送GET请求，指定期望接收JSON格式的响应
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(List.of(MediaType.APPLICATION_JSON));
            HttpEntity<?> entity = new HttpEntity<>(headers);
            // 使用exchange方法可以更灵活地处理请求和响应
            ResponseEntity<String> response = restTemplate.exchange(
                    builder.toUriString(),
                    HttpMethod.GET,
                    entity,
                    String.class
            );
            // 检查响应状态
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                // 手动解析JSON字符串为Map
                return JSONObject.parseObject(responseBody);
            } else {
                System.err.println("HTTP请求失败，状态码: " + response.getStatusCode());
                return null;
            }
        } catch (Exception e){
            System.err.println("调用微信API时发生异常: " + e.getMessage());
        }
        return null;
    }

}
