package com.xnlpgyl.gift.Gift.userAddress.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xnlpgyl.gift.Gift.config.BasePojo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 用户地址表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Getter
@Setter
@TableName("user_address")
public class UserAddress extends BasePojo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 收货人姓名
     */
    @TableField("receiver_name")
    private String receiverName;

    /**
     * 收货人手机号
     */
    @TableField("receiver_phone")
    private String receiverPhone;

    /**
     * 省份
     */
    @TableField("province")
    private String province;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 区/县
     */
    @TableField("district")
    private String district;

    /**
     * 详细地址
     */
    @TableField("detail_address")
    private String detailAddress;

    /**
     * 邮政编码
     */
    @TableField("postal_code")
    private String postalCode;

    /**
     * 是否为默认地址：0-否，1-是
     */
    @TableField("is_default")
    private Byte isDefault;

    /**
     * 地址标签（如：家、公司等）
     */
    @TableField("address_label")
    private String addressLabel;

    @Override
    public String toString() {
        return "UserAddress{" +
                "id=" + getId() +
                ", userId=" + userId +
                ", receiverName='" + receiverName + '\'' +
                ", receiverPhone='" + receiverPhone + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", district='" + district + '\'' +
                ", detailAddress='" + detailAddress + '\'' +
                ", postalCode='" + postalCode + '\'' +
                ", isDefault=" + isDefault +
                ", addressLabel='" + addressLabel + '\'' +
                '}';
    }
}