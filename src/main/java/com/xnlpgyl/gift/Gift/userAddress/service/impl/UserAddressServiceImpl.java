package com.xnlpgyl.gift.Gift.userAddress.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xnlpgyl.gift.Gift.userAddress.entity.UserAddress;
import com.xnlpgyl.gift.Gift.userAddress.mapper.UserAddressMapper;
import com.xnlpgyl.gift.Gift.userAddress.service.IUserAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户地址表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Service
public class UserAddressServiceImpl extends ServiceImpl<UserAddressMapper, UserAddress> implements IUserAddressService {

    @Autowired
    private UserAddressMapper userAddressMapper;

    @Override
    public List<UserAddress> getAddressesByUserId(Long userId) {
        return list(new LambdaQueryWrapper<UserAddress>()
                .eq(UserAddress::getUserId, userId)
                .orderByDesc(UserAddress::getIsDefault)
                .orderByDesc(UserAddress::getUpdateTime));
    }

    @Override
    public UserAddress getDefaultAddressByUserId(Long userId) {
        return getOne(new LambdaQueryWrapper<UserAddress>()
                .eq(UserAddress::getUserId, userId)
                .eq(UserAddress::getIsDefault, 1));
    }

    @Override
    @Transactional
    public boolean setDefaultAddress(Long addressId, Long userId) {
        // 验证地址是否属于该用户
        if (!validateAddressOwnership(addressId, userId)) {
            return false;
        }

        // 先将该用户的所有地址设为非默认
        update(new LambdaUpdateWrapper<UserAddress>()
                .eq(UserAddress::getUserId, userId)
                .set(UserAddress::getIsDefault, 0));

        // 再将指定地址设为默认
        return update(new LambdaUpdateWrapper<UserAddress>()
                .eq(UserAddress::getId, addressId)
                .eq(UserAddress::getUserId, userId)
                .set(UserAddress::getIsDefault, 1));
    }

    @Override
    @Transactional
    public boolean addUserAddress(UserAddress userAddress) {
        // 如果设置为默认地址，先清除该用户的其他默认地址
        if (userAddress.getIsDefault() != null && userAddress.getIsDefault() == 1) {
            update(new LambdaUpdateWrapper<UserAddress>()
                    .eq(UserAddress::getUserId, userAddress.getUserId())
                    .set(UserAddress::getIsDefault, 0));
        } else {
            // 如果用户没有地址，则设为默认地址
            long count = count(new LambdaQueryWrapper<UserAddress>()
                    .eq(UserAddress::getUserId, userAddress.getUserId()));
            if (count == 0) {
                userAddress.setIsDefault((byte) 1);
            } else {
                userAddress.setIsDefault((byte) 0);
            }
        }

        return save(userAddress);
    }

    @Override
    @Transactional
    public boolean updateUserAddress(UserAddress userAddress) {
        // 验证地址是否属于该用户
        if (!validateAddressOwnership(userAddress.getId(), userAddress.getUserId())) {
            return false;
        }

        // 如果设置为默认地址，先清除该用户的其他默认地址
        if (userAddress.getIsDefault() != null && userAddress.getIsDefault() == 1) {
            update(new LambdaUpdateWrapper<UserAddress>()
                    .eq(UserAddress::getUserId, userAddress.getUserId())
                    .ne(UserAddress::getId, userAddress.getId())
                    .set(UserAddress::getIsDefault, 0));
        }

        return updateById(userAddress);
    }

    @Override
    @Transactional
    public boolean deleteUserAddress(Long addressId, Long userId) {
        // 验证地址是否属于该用户
        if (!validateAddressOwnership(addressId, userId)) {
            return false;
        }

        // 检查是否为默认地址
        UserAddress address = getById(addressId);
        boolean isDefault = address != null && address.getIsDefault() != null && address.getIsDefault() == 1;

        // 删除地址
        boolean result = removeById(addressId);

        // 如果删除的是默认地址，需要设置一个新的默认地址
        if (result && isDefault) {
            UserAddress newDefault = getOne(new LambdaQueryWrapper<UserAddress>()
                    .eq(UserAddress::getUserId, userId)
                    .orderByDesc(UserAddress::getUpdateTime)
                    .last("LIMIT 1"));
            
            if (newDefault != null) {
                update(new LambdaUpdateWrapper<UserAddress>()
                        .eq(UserAddress::getId, newDefault.getId())
                        .set(UserAddress::getIsDefault, 1));
            }
        }

        return result;
    }

    @Override
    public boolean validateAddressOwnership(Long addressId, Long userId) {
        if (addressId == null || userId == null) {
            return false;
        }
        
        UserAddress address = getById(addressId);
        return address != null && userId.equals(address.getUserId());
    }
}