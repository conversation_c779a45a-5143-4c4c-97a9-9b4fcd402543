package com.xnlpgyl.gift.Gift.userAddress.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xnlpgyl.gift.Gift.userAddress.entity.UserAddress;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 用户地址表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface UserAddressMapper extends BaseMapper<UserAddress> {

    /**
     * 根据用户ID获取地址列表
     * @param userId 用户ID
     * @return 地址列表
     */
    List<UserAddress> getAddressesByUserId(Long userId);

    /**
     * 根据用户ID获取默认地址
     * @param userId 用户ID
     * @return 默认地址
     */
    UserAddress getDefaultAddressByUserId(Long userId);

    /**
     * 将用户的所有地址设为非默认
     * @param userId 用户ID
     */
    void clearDefaultByUserId(Long userId);
}