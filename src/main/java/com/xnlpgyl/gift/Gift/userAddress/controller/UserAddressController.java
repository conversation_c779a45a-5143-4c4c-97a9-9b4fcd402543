package com.xnlpgyl.gift.Gift.userAddress.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.userAddress.entity.UserAddress;
import com.xnlpgyl.gift.Gift.userAddress.service.IUserAddressService;
import com.xnlpgyl.gift.Gift.utils.ResultCode;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 用户地址管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@RestController
@RequestMapping("/userAddress")
public class UserAddressController extends BaseController {

    @Autowired
    private IUserAddressService userAddressService;

    public UserAddressController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }

    /**
     * 根据用户ID获取地址列表
     */
    @GetMapping("/list/{userId}")
    public ResultMsg<List<UserAddress>> getAddressesByUserId(@PathVariable Long userId) {
        if (userId == null) {
            return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "用户ID不能为空");
        }
        
        List<UserAddress> addresses = userAddressService.getAddressesByUserId(userId);
        return ResultMsg.success(addresses);
    }

    /**
     * 根据用户ID获取默认地址
     */
    @GetMapping("/default/{userId}")
    public ResultMsg<UserAddress> getDefaultAddressByUserId(@PathVariable Long userId) {
        if (userId == null) {
            return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "用户ID不能为空");
        }
        
        UserAddress defaultAddress = userAddressService.getDefaultAddressByUserId(userId);
        return ResultMsg.success(defaultAddress);
    }

    /**
     * 根据ID获取地址详情
     */
    @GetMapping("/{id}")
    public ResultMsg<UserAddress> getAddressById(@PathVariable Long id) {
        if (id == null) {
            return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "地址ID不能为空");
        }
        
        UserAddress address = userAddressService.getById(id);
        if (address == null) {
            return ResultMsg.error(ResultCode.ERROR.getCode(), "地址不存在");
        }
        
        return ResultMsg.success(address);
    }

    /**
     * 分页查询地址
     */
    @PostMapping("/page")
    public ResultMsg<Page<UserAddress>> getAddressesByPage(@RequestBody Page<UserAddress> page) {
        Page<UserAddress> result = userAddressService.page(page);
        return ResultMsg.success(result);
    }

    /**
     * 条件分页查询地址
     */
    @GetMapping("/page/{userId}")
    public ResultMsg<Page<UserAddress>> getAddressesByCondition(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String condition) {
        
        if (userId == null) {
            return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "用户ID不能为空");
        }
        
        Page<UserAddress> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<UserAddress> queryWrapper = new LambdaQueryWrapper<UserAddress>()
                .eq(UserAddress::getUserId, userId);
        
        if (StringUtils.isNotBlank(condition)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(UserAddress::getReceiverName, condition)
                    .or().like(UserAddress::getReceiverPhone, condition)
                    .or().like(UserAddress::getProvince, condition)
                    .or().like(UserAddress::getCity, condition)
                    .or().like(UserAddress::getDistrict, condition)
                    .or().like(UserAddress::getDetailAddress, condition)
                    .or().like(UserAddress::getAddressLabel, condition));
        }
        
        queryWrapper.orderByDesc(UserAddress::getIsDefault)
                   .orderByDesc(UserAddress::getUpdateTime);
        
        Page<UserAddress> result = userAddressService.page(page, queryWrapper);
        return ResultMsg.success(result);
    }

    /**
     * 添加地址
     */
    @PostMapping("/add")
    public ResultMsg<Boolean> addAddress(@Valid @RequestBody UserAddress userAddress) {
        if (userAddress.getUserId() == null) {
            return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "用户ID不能为空");
        }
        
        if (StringUtils.isAnyBlank(userAddress.getReceiverName(), userAddress.getReceiverPhone(),
                userAddress.getProvince(), userAddress.getCity(), userAddress.getDetailAddress())) {
            return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "收货人姓名、手机号、省份、城市、详细地址不能为空");
        }
        
        boolean result = userAddressService.addUserAddress(userAddress);
        if (result) {
            return ResultMsg.success(true);
        } else {
            return ResultMsg.error(ResultCode.ERROR.getCode(), "添加地址失败");
        }
    }

    /**
     * 更新地址
     */
    @PutMapping("/update")
    public ResultMsg<Boolean> updateAddress(@Valid @RequestBody UserAddress userAddress) {
        if (userAddress.getId() == null || userAddress.getUserId() == null) {
            return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "地址ID和用户ID不能为空");
        }
        
        if (StringUtils.isAnyBlank(userAddress.getReceiverName(), userAddress.getReceiverPhone(),
                userAddress.getProvince(), userAddress.getCity(), userAddress.getDetailAddress())) {
            return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "收货人姓名、手机号、省份、城市、详细地址不能为空");
        }
        
        boolean result = userAddressService.updateUserAddress(userAddress);
        if (result) {
            return ResultMsg.success(true);
        } else {
            return ResultMsg.error(ResultCode.ERROR.getCode(), "更新地址失败");
        }
    }

    /**
     * 删除地址
     */
    @DeleteMapping("/delete/{id}/{userId}")
    public ResultMsg<Boolean> deleteAddress(@PathVariable Long id, @PathVariable Long userId) {
        if (id == null || userId == null) {
            return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "地址ID和用户ID不能为空");
        }
        
        boolean result = userAddressService.deleteUserAddress(id, userId);
        if (result) {
            return ResultMsg.success(true);
        } else {
            return ResultMsg.error(ResultCode.ERROR.getCode(), "删除地址失败");
        }
    }

    /**
     * 批量删除地址
     */
    @DeleteMapping("/deleteBatch/{userId}")
    public ResultMsg<Boolean> deleteAddresses(@PathVariable Long userId, @RequestBody List<Long> ids) {
        if (userId == null || ids == null || ids.isEmpty()) {
            return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "用户ID和地址ID列表不能为空");
        }
        
        boolean allSuccess = true;
        for (Long id : ids) {
            boolean result = userAddressService.deleteUserAddress(id, userId);
            if (!result) {
                allSuccess = false;
            }
        }
        
        if (allSuccess) {
            return ResultMsg.success(true);
        } else {
            return ResultMsg.error(ResultCode.ERROR.getCode(), "部分地址删除失败");
        }
    }

    /**
     * 设置默认地址
     */
    @PutMapping("/setDefault/{id}/{userId}")
    public ResultMsg<Boolean> setDefaultAddress(@PathVariable Long id, @PathVariable Long userId) {
        if (id == null || userId == null) {
            return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "地址ID和用户ID不能为空");
        }
        
        boolean result = userAddressService.setDefaultAddress(id, userId);
        if (result) {
            return ResultMsg.success(true);
        } else {
            return ResultMsg.error(ResultCode.ERROR.getCode(), "设置默认地址失败");
        }
    }
}