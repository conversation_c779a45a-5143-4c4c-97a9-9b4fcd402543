package com.xnlpgyl.gift.Gift.userAddress.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xnlpgyl.gift.Gift.userAddress.entity.UserAddress;

import java.util.List;

/**
 * <p>
 * 用户地址表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface IUserAddressService extends IService<UserAddress> {

    /**
     * 根据用户ID获取地址列表
     * @param userId 用户ID
     * @return 地址列表
     */
    List<UserAddress> getAddressesByUserId(Long userId);

    /**
     * 根据用户ID获取默认地址
     * @param userId 用户ID
     * @return 默认地址
     */
    UserAddress getDefaultAddressByUserId(Long userId);

    /**
     * 设置默认地址
     * @param addressId 地址ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean setDefaultAddress(Long addressId, Long userId);

    /**
     * 添加用户地址
     * @param userAddress 用户地址
     * @return 是否成功
     */
    boolean addUserAddress(UserAddress userAddress);

    /**
     * 更新用户地址
     * @param userAddress 用户地址
     * @return 是否成功
     */
    boolean updateUserAddress(UserAddress userAddress);

    /**
     * 删除用户地址
     * @param addressId 地址ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteUserAddress(Long addressId, Long userId);

    /**
     * 验证地址是否属于用户
     * @param addressId 地址ID
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    boolean validateAddressOwnership(Long addressId, Long userId);
}