package com.xnlpgyl.gift.Gift.config;

import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.reader.ObjectReader;
import org.bson.types.ObjectId;
import java.lang.reflect.Type;

public class ObjectIdDeserializer implements ObjectReader<ObjectId> {
    @Override
    public ObjectId readObject(JSONReader jsonReader, Type fieldType, Object fieldName, long features) {
        String value = jsonReader.readString();
        return value == null ? null : new ObjectId(value);
    }
}
