package com.xnlpgyl.gift.Gift.config.interceptor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.dao.RequestLogDao;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 请求日志拦截器
 * 记录所有HTTP请求的详细信息到MongoDB
 */
@Component
public class RequestLogInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(RequestLogInterceptor.class);

    @Autowired
    private RequestLogDao requestLogDao;
    
    /**
     * 是否记录请求体
     */
    @Value("${request.log.record-request-body:true}")
    private boolean recordRequestBody;
    
    /**
     * 是否记录响应体
     */
    @Value("${request.log.record-response-body:true}")
    private boolean recordResponseBody;
    
    /**
     * 响应体最大记录长度
     */
    @Value("${request.log.max-response-body-length:1000}")
    private int maxResponseBodyLength;

    private static final ThreadLocal<Long> START_TIME = new ThreadLocal<>();
    private static final ThreadLocal<JSONObject> REQUEST_LOG = new ThreadLocal<>();

    /**
     * 在请求处理之前调用
     * 记录请求开始时间和请求信息
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        START_TIME.set(System.currentTimeMillis());

        // 创建请求日志对象
        JSONObject logInfo = new JSONObject();
        logInfo.put("requestTime", System.currentTimeMillis());
        logInfo.put("requestMethod", request.getMethod());
        logInfo.put("requestUri", request.getRequestURI());
        logInfo.put("queryString", request.getQueryString());
        logInfo.put("requestParams", request.getParameterMap());
        logInfo.put("clientIp", getClientIp(request));
        logInfo.put("userAgent", request.getHeader("User-Agent"));

        // 记录请求头
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            // 排除一些敏感的请求头，如Authorization
            if (!"Authorization".equalsIgnoreCase(headerName) &&
                !"Cookie".equalsIgnoreCase(headerName)) {
                headers.put(headerName, request.getHeader(headerName));
            }
        }
        logInfo.put("headers", headers);

        // 记录请求参数
        Map<String, String[]> parameterMap = request.getParameterMap();
        if (parameterMap != null && !parameterMap.isEmpty()) {
            logInfo.put("parameters", JSON.toJSONString(parameterMap));
        }

        // 根据配置决定是否记录请求体
        if (recordRequestBody && request instanceof ContentCachingRequestWrapper) {
            try {
                ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) request;
                byte[] buf = wrapper.getContentAsByteArray();
                if (buf.length > 0) {
                    String payload = new String(buf, 0, buf.length, StandardCharsets.UTF_8);
                    try {
                        // 尝试解析为JSON
                        JSONObject jsonPayload = JSON.parseObject(payload);
                        // 如果包含密码字段，进行脱敏处理
                        if (jsonPayload.containsKey("password")) {
                            jsonPayload.put("password", "******");
                        }
                        logInfo.put("requestBody", jsonPayload);
                    } catch (Exception e) {
                        // 如果不是JSON格式，直接存储原始内容
                        logInfo.put("requestBody", payload);
                    }
                }
            } catch (Exception e) {
                logger.warn("记录请求体时发生错误", e);
            }
        }

        REQUEST_LOG.set(logInfo);
        return true;
    }

    /**
     * 在请求完成之后调用
     * 记录响应信息和处理时间
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        Long startTime = START_TIME.get();
        JSONObject logInfo = REQUEST_LOG.get();

        if (logInfo != null) {
            // 记录响应信息
            logInfo.put("responseTime", System.currentTimeMillis());
            logInfo.put("processingTime", System.currentTimeMillis() - startTime);
            logInfo.put("responseStatus", response.getStatus());

            // 根据配置决定是否记录响应体
            if (recordResponseBody && response instanceof ContentCachingResponseWrapper) {
                try {
                    ContentCachingResponseWrapper wrapper = (ContentCachingResponseWrapper) response;
                    byte[] buf = wrapper.getContentAsByteArray();
                    if (buf.length > 0) {
                        String payload = new String(buf, 0, buf.length, StandardCharsets.UTF_8);
                        try {
                            // 尝试解析为JSON
                            JSONObject jsonPayload = JSON.parseObject(payload);
                            logInfo.put("responseBody", jsonPayload);
                        } catch (Exception e) {
                            // 如果不是JSON格式或超过最大长度，进行截断
                            if (payload.length() > maxResponseBodyLength) {
                                logInfo.put("responseBody", payload.substring(0, maxResponseBodyLength) + "...(truncated)");
                                logInfo.put("responseBodyTruncated", true);
                                logInfo.put("originalResponseLength", payload.length());
                            } else {
                                logInfo.put("responseBody", payload);
                            }
                        }
                    }

                    // 重要：重新写入响应体，否则客户端将收不到响应
                    wrapper.copyBodyToResponse();
                } catch (Exception e) {
                    logger.warn("记录响应体时发生错误", e);
                }
            }

            // 记录异常信息
            if (ex != null) {
                logInfo.put("exception", ex.getMessage());
                logInfo.put("exceptionType", ex.getClass().getName());
            }

            // 保存日志到MongoDB
            try {
                requestLogDao.saveOrUpdate(logInfo, RequestLogDao.COLLECTION_NAME_REQUEST_LOG);
            } catch (Exception e) {
                // 记录日志失败不应影响正常业务流程
                e.printStackTrace();
            }
        }

        // 清理ThreadLocal变量，防止内存泄漏
        START_TIME.remove();
        REQUEST_LOG.remove();
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 对于通过多个代理的情况，第一个IP为客户端真实IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }
}
