package com.xnlpgyl.gift.Gift.config;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class CorsConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**") // 允许所有路径
                .allowedOriginPatterns("*") // 允许所有来源（开发环境）
                .allowedOrigins(
                    "https://www.xnlpgyl.com",
                    "http://www.xnlpgyl.com",
                    "http://localhost:3000",
                    "http://localhost:8080",
                    "http://127.0.0.1:8080",
                    "http://***************:8080"
                ) // 允许的跨域来源
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH") // 允许的HTTP方法
                .allowedHeaders("*") // 允许的请求头
                .allowCredentials(true) // 是否允许携带凭证（如Cookie）
                .maxAge(3600); // 预检请求缓存时间（秒）
    }
}
