package com.xnlpgyl.gift.Gift.config.task;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.dao.RequestLogDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 请求日志清理定时任务
 * 定期清除过期的请求日志
 */
@Component
public class RequestLogCleanTask {

    private static final Logger logger = LoggerFactory.getLogger(RequestLogCleanTask.class);

    @Autowired
    private RequestLogDao requestLogDao;

    /**
     * 日志保留天数，默认30天
     */
    @Value("${request.log.retention.days:30}")
    private int logRetentionDays;

    /**
     * 定时清除过期日志
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredLogs() {
        logger.info("开始清理过期请求日志，保留最近{}天的日志", logRetentionDays);

        try {
            // 计算过期时间戳（当前时间减去保留天数）
            long expireTime = System.currentTimeMillis() - (logRetentionDays * 24 * 60 * 60 * 1000L);

            // 创建查询条件：requestTime小于过期时间
            Query query = new Query();
            query.addCriteria(Criteria.where("requestTime").lt(expireTime));

            // 查询要删除的日志数量
            long count = requestLogDao.count(query, RequestLogDao.COLLECTION_NAME_REQUEST_LOG);

            if (count > 0) {
                // 删除过期日志
                requestLogDao.delete(query, RequestLogDao.COLLECTION_NAME_REQUEST_LOG);
                logger.info("成功清理{}条过期请求日志", count);

                // 记录清理结果到MongoDB（可选）
                JSONObject cleanRecord = new JSONObject();
                cleanRecord.put("cleanTime", System.currentTimeMillis());
                cleanRecord.put("retentionDays", logRetentionDays);
                cleanRecord.put("deletedCount", count);
                cleanRecord.put("expireTimeBefore", expireTime);
                requestLogDao.saveOrUpdate(cleanRecord, "requestLogCleanHistory");
            } else {
                logger.info("没有过期的请求日志需要清理");
            }
        } catch (Exception e) {
            logger.error("清理过期请求日志失败", e);
        }
    }
}
