package com.xnlpgyl.gift.Gift.config;

import com.xnlpgyl.gift.Gift.utils.ResultCode;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.sql.SQLSyntaxErrorException;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理自定义业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResultMsg<?> handleBusinessException(BusinessException e) {
        log.error("业务异常：{}", e.getMessage(), e);
        return ResultMsg.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理参数校验异常 (JSR-303)
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultMsg<?> handleValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        String message = bindingResult.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.error("参数校验异常：{}", message);
        return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), message);
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResultMsg<?> handleBindException(BindException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.error("参数绑定异常：{}", message);
        return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), message);
    }

    /**
     * 处理请求参数格式错误
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResultMsg<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.error("请求参数格式错误：{}", e.getMessage());
        return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "请求参数格式错误");
    }

    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResultMsg<?> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        log.error("不支持的请求方法：{}", e.getMessage());
        return ResultMsg.error(ResultCode.METHOD_NOT_ALLOWED);
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResultMsg<?> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        log.error("参数类型不匹配：{}", e.getMessage());
        return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "参数类型不匹配");
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResultMsg<?> handleConstraintViolationException(ConstraintViolationException e) {
        String message = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        log.error("参数校验异常：{}", message);
        return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), message);
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResultMsg<?> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.error("文件上传大小超限：{}", e.getMessage());
        return ResultMsg.error(ResultCode.ERROR.getCode(), "上传文件大小超出限制");
    }

    /**
     * 处理数据库唯一约束异常
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public ResultMsg<?> handleDuplicateKeyException(DuplicateKeyException e) {
        log.error("数据重复：{}", e.getMessage());
        return ResultMsg.error(ResultCode.ERROR.getCode(), "数据已存在");
    }

    /**
     * 处理SQL语法错误异常
     */
    @ExceptionHandler(SQLSyntaxErrorException.class)
    public ResultMsg<?> handleSQLSyntaxErrorException(SQLSyntaxErrorException e) {
        log.error("SQL语法错误：{}", e.getMessage());
        return ResultMsg.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "数据库操作异常");
    }

    /**
     * 处理请求参数缺失异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResultMsg<?> handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        log.error("请求参数缺失：{}", e.getMessage());
        return ResultMsg.error(ResultCode.PARAM_ERROR.getCode(), "请求参数缺失: " + e.getParameterName());
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public ResultMsg<?> handleNullPointerException(NullPointerException e) {
        log.error("空指针异常：", e);
        return ResultMsg.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "系统内部错误");
    }
    @ExceptionHandler(BadCredentialsException.class)
    public ResultMsg<?> handleBadCredentialsException(BadCredentialsException e) {
        log.error("认证失败：", e);
        return ResultMsg.error(ResultCode.USER_PASSWORD_ERROR);
    }

    @ExceptionHandler(AccountExpiredException.class)
    public ResultMsg<?> AccountExpiredException(AccountExpiredException e) {
        log.error("账号密码错误：", e);
        return ResultMsg.error(ResultCode.USER_PASSWORD_ERROR);
    }

    @ExceptionHandler(RuntimeException.class)
    public ResultMsg<?> RuntimeException(RuntimeException e) {
        log.error("未授权：", e);
        return ResultMsg.error(e.getMessage());
    }
    @ExceptionHandler(AuthenticationServiceException.class)
    public ResultMsg<?> AuthenticationServiceException(AuthenticationServiceException e) {
        log.error("未授权：", e);
        return ResultMsg.error(e.getMessage());
    }
    //NoResourceFoundException
    @ExceptionHandler(NoResourceFoundException.class)
    public ResultMsg<?> handleNoResourceFoundException(NoResourceFoundException e) {
        log.error("资源不存在：", e);
        return ResultMsg.error(ResultCode.NOT_FOUND);
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public ResultMsg<?> handleException(Exception e) {
        log.error("未知异常：", e);
        return ResultMsg.error(ResultCode.INTERNAL_SERVER_ERROR);
    }
}