package com.xnlpgyl.gift.Gift.config;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Repository
public class BaseDao {

    private MongoTemplate mongoTemplate;

    @Autowired
    public void setMongoTemplate(@Lazy MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public JSONObject getById(String _id,String collectionName) {
        return mongoTemplate.findById(_id, JSONObject.class, collectionName);
    }

    public JSONObject saveOrUpdate(JSONObject giftGoods,String collectionName) {
        try {
            return mongoTemplate.save(giftGoods, collectionName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public boolean deleteById(String _id,String collectionName) {
        try {
            mongoTemplate.remove(Objects.requireNonNull(mongoTemplate.findById(_id, JSONObject.class,collectionName)), collectionName);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public List<JSONObject> page(Query page,String collectionName) {
        try {
            return mongoTemplate.find(page, JSONObject.class,  collectionName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public long count(Query query,String collectionName) {
        try {
            return mongoTemplate.count(query, JSONObject.class,  collectionName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public List<JSONObject> getAllList(String collectionName) {
        try {
            return mongoTemplate.findAll(JSONObject.class, collectionName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public JSONObject getByField(String field,String value,String collectionName) {
        try {
            return mongoTemplate.findOne(new Query(Criteria.where(field).is(value)), JSONObject.class, collectionName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    //多个字段的条件查询
    public List<JSONObject> getByFields(Map<String, Object> fields, String collectionName) {
        try {
            Query query = new Query();
            fields.forEach((k, v) -> query.addCriteria(Criteria.where(k).is(v)));
            return mongoTemplate.find(query, JSONObject.class, collectionName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public JSONObject updateStatus(JSONObject scheme, String giftScheme) {
        try {
            return mongoTemplate.save(scheme, giftScheme);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public MongoPage<LinkedHashMap<String, Object>> findPage(Query query, Integer page, Integer size, String collectionNameLoginLog) {
        try {
            MongoPage<LinkedHashMap<String, Object>> mongoPage = new MongoPage<LinkedHashMap<String, Object>>();
            mongoPage.setPageNum(page);
            mongoPage.setPageSize(size);
            mongoPage.setTotalCount(mongoTemplate.count(query, JSONObject.class, collectionNameLoginLog));
            query.skip((long) (page - 1) * size);
            query.limit(size);
            mongoPage.setList(mongoTemplate.find(query, JSONObject.class, collectionNameLoginLog));
            return mongoPage;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<JSONObject> find(Query query, String collectionNameLoginLog) {
        try {
            return mongoTemplate.find(query, JSONObject.class, collectionNameLoginLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public void delete(Query query, String collectionNameRequestLog) {

        try {
            mongoTemplate.remove(query, JSONObject.class, collectionNameRequestLog);
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public JSONObject findById(String id, String collectionNameRequestLog) {
        try {
            return mongoTemplate.findById(id, JSONObject.class, collectionNameRequestLog);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 更新几个字段
     * @param _id 主键ID
     * @param update 更新内容
     * @param collectionNameRequestLog 集合名称
     * @return
     */
    public JSONObject updateFieldsById(String _id, JSONObject update, String collectionNameRequestLog) {
        try {
            Update update1 = new Update();
            update.forEach((k, v) -> update1.set(k, v));
            return mongoTemplate.findAndModify(Query.query(Criteria.where("_id").is(_id)), update1, JSONObject.class, collectionNameRequestLog);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
