package com.xnlpgyl.gift.Gift.config;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.writer.ObjectWriter;
import org.bson.types.ObjectId;
import java.lang.reflect.Type;

public class ObjectIdSerializer implements ObjectWriter<ObjectId> {
    @Override
    public void write(JSONWriter writer, Object object, Object fieldName, Type fieldType, long features) {
        ObjectId objectId = (ObjectId) object;
        if (objectId == null) {
            writer.writeNull();
        } else {
            writer.writeString(objectId.toString());
        }
    }
}

