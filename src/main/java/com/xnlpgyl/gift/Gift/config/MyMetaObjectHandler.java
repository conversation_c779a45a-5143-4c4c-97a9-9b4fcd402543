package com.xnlpgyl.gift.Gift.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createTime", LocalDateTime::now, LocalDateTime.class);
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime::now, LocalDateTime.class);
        this.strictInsertFill(metaObject, "deleted", () -> false, Boolean.class);
        // TODO: 需要集成登录用户信息后设置createBy和updateBy
        this.strictInsertFill(metaObject, "createBy", () -> 1L, Long.class);
        this.strictInsertFill(metaObject, "updateBy", () -> 1L, Long.class);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime::now, LocalDateTime.class);
        // TODO: 需要集成登录用户信息后设置updateBy
        this.strictUpdateFill(metaObject, "updateBy", () -> 1L, Long.class);
    }
}