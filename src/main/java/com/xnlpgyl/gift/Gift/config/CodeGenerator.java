package com.xnlpgyl.gift.Gift.config;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;

import java.sql.Types;
import java.util.Collections;

/**
 * 将指定的mysql数据库里面的指定表生成 MyBatis-Plus代码 包括 entity、mapper、service、controller、xml等文件
 * 使用方法：修改下面的常量值，然后运行main
 * 代码生成器 - 用于生成MyBatis-Plus代码
 * 该类使用FastAutoGenerator进行代码生成，配置了一些常用的配置项，如作者信息、输出目录、目录权限等。
 * 并使用Velocity模板引擎生成代码，并自定义了数据库类型到Java类型的转换规则，特别处理了SMALLINT类型为Integer类型。
 * 该类可以根据需要进行配置修改，生成的代码可以直接运行，但需要注意修改数据库连接信息、数据库表名、包名等配置项。
 * 该类的main方法是程序入口，执行该方法即可生成代码。
 * <AUTHOR>
 */
public class CodeGenerator {
    private static final String JDBC_URL = "******************************************************************************************************************";
    private static final String DB_USERNAME = "root";
    private static final String DB_PASSWORD = "123q123q";
    private static final String PACKAGE_NAME = "com.xnlpgyl.gift.Gift";
    private static final String OUTPUT_DIR = "src/main/java";
    private static final String XML_OUTPUT_DIR = "src/main/resources/mapper";
    private static final String AUTHOR = "han_qi";
    /**
     * 指定需要处理的数据表名称 数据源，就是需要把数据表映射成实体类、DAO、Service、Controller等的数据表名称，从低级到高级，例如 t_sys_user_role -> SysUserRole->SysUserRoleMapper->SysUserRoleDao->SysUserRoleService->SysUserRoleServiceImpl->SysUserRoleController
     * 因此从软件工程而言，我们必须根据具体业务逻辑来确定表名，并且确定每个表的字段，和表与表之间的关系。然后根据表名生成对应的实体类、DAO、Service、Controller等代码。
     * 因此每个表需要尽量保持独一，单一职责，避免深度耦合。表与表之间的关系也尽量保持单一，避免深度耦合。
     * 因此，我们可以将表名按照业务逻辑分组，然后为每个分组定义一个模块，然后在模块中定义相关的实体类、DAO、Service、Controller等代码。
     * 例如，我们可以将用户相关的表放在一个模块中，将角色相关的表放在另一个模块中，这样可以避免生成过多的实体类、DAO、Service、Controller等代码。
     * 这样，我们可以根据业务需求，灵活调整代码生成策略，生成符合要求的代码。
     * 表与表之间，我们使用第三张表来建立表与表之间的关系，例如，用户表和角色表之间通过用户角色表建立关系。
     * 这样，我们可以根据第三张表的关系，自动生成代码，避免手工编写代码。
     */
    private static final String[] SOURCE_TABLE_NAMES = {"t_sys_user_role"};
    /**
     * 生成实例对象时可以去掉的数据表前缀 t_sys_user_role -> sys_user_role -> SysUserRole
     */
    private static final String[] tablePrefix = {"t_", "c_"};
    /**
     * 模块名称，用于生成代码的包层级结构 这个最好和表名一致，否则会生成多个模块，不方便管理
     */
    private static final String MODULE_NAME = "SysUserRole";
    /**
     * 主程序入口，用于执行代码自动生成任务
     * @param args 命令行参数（未使用）
     */
    public static void main(String[] args) {
        /*
          使用FastAutoGenerator创建代码生成器实例
          配置MySQL数据库连接信息及认证参数
          JDBC URL指向本地gift数据库，包含时区和字符集配置
         */
        FastAutoGenerator.create(JDBC_URL, DB_USERNAME, DB_PASSWORD)
                /*
                  全局配置项
                  设置作者信息、输出目录及目录操作权限
                 */
                .globalConfig(builder -> {
                    builder.author(AUTHOR) // 设置生成代码的作者署名
                            .outputDir(OUTPUT_DIR) // 指定生成代码的根输出目录
                            .disableOpenDir(); // 生成后禁止自动打开输出目录

                })
                /*
                  数据源配置
                  自定义JDBC类型到Java类型的转换规则
                  特别处理SMALLINT类型为Integer类型
                 */
                .dataSourceConfig(builder ->
                        builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                            int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                            if (typeCode == Types.SMALLINT) {
                                // 将数据库SMALLINT类型映射为Java Integer类型
                                return DbColumnType.INTEGER;
                            }
                            return typeRegistry.getColumnType(metaInfo);
                        })
                )
                /*
                  包结构配置
                  定义生成代码的包层级结构及资源文件路径
                 */
                .packageConfig(builder ->
                        builder.parent(PACKAGE_NAME) // 设置父包名称空间
                                .moduleName(MODULE_NAME) // 定义模块子包名称
                                .pathInfo(Collections.singletonMap(OutputFile.xml,  XML_OUTPUT_DIR)) // 指定MyBatis XML映射文件输出路径
                )
                /*
                  生成策略配置
                  定义表处理规则及代码生成模板配置
                 */
                .strategyConfig(builder ->
                        builder.addInclude(SOURCE_TABLE_NAMES) // 指定需要处理的数据表名称
                                .addTablePrefix(tablePrefix) // 过滤表名前缀，生成时去除指定前缀
                                .entityBuilder()
                                .enableLombok() // 启用Lombok注解生成
                                .enableTableFieldAnnotation() // 生成字段注解元数据
                                .controllerBuilder()
                                .enableRestStyle() // 生成REST风格控制器
                )
                /*
                  模板引擎配置
                  使用Velocity模板引擎生成代码（默认引擎）
                 */
                .templateEngine(new VelocityTemplateEngine())
                .execute(); // 执行代码生成操作
    }
}
