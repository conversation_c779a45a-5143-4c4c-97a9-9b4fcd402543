package com.xnlpgyl.gift.Gift.config;


import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;

@Configuration
public class RestTemplateConfig {
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        RestTemplate restTemplate = builder.build();
        List<HttpMessageConverter<?>> messageConverters = restTemplate.getMessageConverters();
        messageConverters.add(new MappingJackson2HttpMessageConverter());
        restTemplate.setMessageConverters(messageConverters);
        // 确保RestTemplate支持text/plain类型的响应
        restTemplate.getMessageConverters().stream()
                .filter(converter -> converter.getClass().getSimpleName().contains("MappingJackson2HttpMessageConverter"))
                .forEach(converter -> {
                    // 添加对text/plain的支持
                    ((org.springframework.http.converter.json.MappingJackson2HttpMessageConverter) converter)
                            .setSupportedMediaTypes(java.util.Arrays.asList(
                                    MediaType.APPLICATION_JSON,
                                    MediaType.TEXT_PLAIN,
                                    MediaType.APPLICATION_OCTET_STREAM
                            ));
                });
        return restTemplate;
    }
}
