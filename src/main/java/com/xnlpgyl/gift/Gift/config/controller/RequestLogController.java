package com.xnlpgyl.gift.Gift.config.controller;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.config.MongoPage;
import com.xnlpgyl.gift.Gift.config.dao.RequestLogDao;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;

/**
 * 请求日志控制器
 * 提供查询和管理请求日志的接口
 */
@RestController
@RequestMapping("/system/requestLog")
public class RequestLogController extends BaseController {

    @Autowired
    private RequestLogDao requestLogDao;

    public RequestLogController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }

    /**
     * 分页查询请求日志
     *
     * @param page      页码
     * @param size      每页大小
     * @param uri       请求URI（可选）
     * @param method    请求方法（可选）
     * @param status    响应状态码（可选）
     * @param startTime 开始时间（可选）
     * @param endTime   结束时间（可选）
     * @param clientIp  客户端IP（可选）
     * @return 请求日志列表
     */
    @GetMapping("/list")
    public ResultMsg<MongoPage<LinkedHashMap<String, Object>>> getRequestLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String uri,
            @RequestParam(required = false) String method,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @RequestParam(required = false) String clientIp) {

        Query query = new Query();

        // 添加查询条件
        if (StringUtils.isNotBlank(uri)) {
            query.addCriteria(Criteria.where("requestUri").regex(uri));
        }

        if (StringUtils.isNotBlank(method)) {
            query.addCriteria(Criteria.where("requestMethod").is(method.toUpperCase()));
        }

        if (status != null) {
            query.addCriteria(Criteria.where("responseStatus").is(status));
        }

        if (startTime != null && endTime != null) {
            query.addCriteria(Criteria.where("requestTime").gte(startTime).lte(endTime));
        } else if (startTime != null) {
            query.addCriteria(Criteria.where("requestTime").gte(startTime));
        } else if (endTime != null) {
            query.addCriteria(Criteria.where("requestTime").lte(endTime));
        }

        if (StringUtils.isNotBlank(clientIp)) {
            query.addCriteria(Criteria.where("clientIp").is(clientIp));
        }

        // 按请求时间降序排序
        query.with(Sort.by(Sort.Direction.DESC, "requestTime"));

        MongoPage<LinkedHashMap<String, Object>> mongoPage = requestLogDao.findPage(query, page, size, RequestLogDao.COLLECTION_NAME_REQUEST_LOG);
        return ResultMsg.success(mongoPage);
    }

    /**
     * 获取请求日志详情
     * @param id 日志ID
     * @return 日志详情
     */
    @GetMapping("/detail/{id}")
    public ResultMsg<JSONObject> getRequestLogDetail(@PathVariable String id) {
        JSONObject log = requestLogDao.findById(id, RequestLogDao.COLLECTION_NAME_REQUEST_LOG);
        if (log == null) {
            return ResultMsg.error("日志不存在");
        }
        return ResultMsg.success(log);
    }

    /**
     * 获取请求统计信息
     * @param hours 最近几小时（默认24小时）
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResultMsg<JSONObject> getRequestStats(@RequestParam(defaultValue = "24") Integer hours) {
        // 计算最近hours小时的时间戳
        long startTime = System.currentTimeMillis() - (hours * 60 * 60 * 1000L);

        // 查询总请求数
        Query totalQuery = new Query();
        totalQuery.addCriteria(Criteria.where("requestTime").gte(startTime));
        long totalCount = requestLogDao.count(totalQuery, RequestLogDao.COLLECTION_NAME_REQUEST_LOG);

        // 查询成功请求数（状态码2xx）
        Query successQuery = new Query();
        successQuery.addCriteria(Criteria.where("requestTime").gte(startTime));
        successQuery.addCriteria(Criteria.where("responseStatus").gte(200).lt(300));
        long successCount = requestLogDao.count(successQuery, RequestLogDao.COLLECTION_NAME_REQUEST_LOG);

        // 查询客户端错误请求数（状态码4xx）
        Query clientErrorQuery = new Query();
        clientErrorQuery.addCriteria(Criteria.where("requestTime").gte(startTime));
        clientErrorQuery.addCriteria(Criteria.where("responseStatus").gte(400).lt(500));
        long clientErrorCount = requestLogDao.count(clientErrorQuery, RequestLogDao.COLLECTION_NAME_REQUEST_LOG);

        // 查询服务器错误请求数（状态码5xx）
        Query serverErrorQuery = new Query();
        serverErrorQuery.addCriteria(Criteria.where("requestTime").gte(startTime));
        serverErrorQuery.addCriteria(Criteria.where("responseStatus").gte(500).lt(600));
        long serverErrorCount = requestLogDao.count(serverErrorQuery, RequestLogDao.COLLECTION_NAME_REQUEST_LOG);

        // 返回统计结果
        JSONObject stats = new JSONObject();
        stats.put("totalCount", totalCount);
        stats.put("successCount", successCount);
        stats.put("clientErrorCount", clientErrorCount);
        stats.put("serverErrorCount", serverErrorCount);
        stats.put("hours", hours);

        return ResultMsg.success(stats);
    }

    /**
     * 清除过期日志
     * @param days 保留最近几天的日志（默认30天）
     * @return 清除结果
     */
    @DeleteMapping("/clean")
    public ResultMsg<JSONObject> cleanExpiredLogs(@RequestParam(defaultValue = "30") Integer days) {
        // 计算过期时间戳
        long expireTime = System.currentTimeMillis() - (days * 24 * 60 * 60 * 1000L);

        Query query = new Query();
        query.addCriteria(Criteria.where("requestTime").lt(expireTime));

        long count = requestLogDao.count(query, RequestLogDao.COLLECTION_NAME_REQUEST_LOG);
        requestLogDao.delete(query, RequestLogDao.COLLECTION_NAME_REQUEST_LOG);

        JSONObject result = new JSONObject();
        result.put("deletedCount", count);
        result.put("retentionDays", days);

        return ResultMsg.success(result);
    }
}
