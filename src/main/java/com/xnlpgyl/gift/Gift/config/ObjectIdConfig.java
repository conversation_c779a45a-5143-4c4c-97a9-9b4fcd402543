package com.xnlpgyl.gift.Gift.config;

import com.alibaba.fastjson2.JSONFactory;
import org.bson.types.ObjectId;

public class ObjectIdConfig {
    public static void configure() {
        // 注册序列化器
        JSONFactory.getDefaultObjectWriterProvider().register(ObjectId.class, new ObjectIdSerializer());

        // 注册反序列化器
        JSONFactory.getDefaultObjectReaderProvider().register(ObjectId.class, new ObjectIdDeserializer());
    }
}
