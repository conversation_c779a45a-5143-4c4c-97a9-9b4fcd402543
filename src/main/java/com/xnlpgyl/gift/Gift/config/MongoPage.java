package com.xnlpgyl.gift.Gift.config;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Query;

import java.util.LinkedHashMap;
import java.util.List;

@Data
public class MongoPage<J extends LinkedHashMap<String, Object>> {

    private Integer pageNum;
    private List<JSONObject> content;
    private Integer pageSize;
    private long totalCount;

    public MongoPage(Integer pageNum, Integer pageSize, List<JSONObject> content) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.content = content;
    }

    public MongoPage() {
        this.pageNum = 1;
        this.pageSize = 10;
        this.content = null;
    }

    public static void getPageQuery(Query query, int pageNum, int pageSize) {
        pageNum = Math.max(pageNum, 1);
        pageSize = Math.max(pageSize, 10);
        query.skip((long) (pageNum-1) * pageSize);
        query.limit(pageSize);
        query.with(Sort.by(Sort.Direction.DESC, "updateTime"));
    }

    public void setTotalCount(long count) {
        this.pageNum = (int) count;
    }

    public void setList(List<JSONObject> jsonObjects) {
        this.content =  jsonObjects;
    }
}
