package com.xnlpgyl.gift.Gift.config;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Getter;

@Getter
public class BaseController {
    private final HttpServletRequest request;
    private final HttpServletResponse response;
    public BaseController(HttpServletRequest request, HttpServletResponse response){
        this.request = request;
        this.response = response;
    }
}
