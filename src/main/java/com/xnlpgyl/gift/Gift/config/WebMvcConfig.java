package com.xnlpgyl.gift.Gift.config;

import com.xnlpgyl.gift.Gift.config.interceptor.RequestLogInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Web MVC配置类
 * 用于注册拦截器和过滤器
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private RequestLogInterceptor requestLogInterceptor;

    /**
     * 添加拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册请求日志拦截器，拦截所有请求
        registry.addInterceptor(requestLogInterceptor)
                .addPathPatterns("/**")
                // 排除一些不需要记录日志的路径
                .excludePathPatterns(
                        "/swagger-resources/**",
                        "/webjars/**",
                        "/v3/api-docs/**",
                        "/swagger-ui/**",
                        "/error",
                        "/favicon.ico"
                );
    }

    /**
     * 注册内容缓存过滤器
     * 用于包装请求和响应，使其内容可以被多次读取
     */
    @Bean
    public FilterRegistrationBean<OncePerRequestFilter> contentCachingFilter() {
        FilterRegistrationBean<OncePerRequestFilter> registrationBean = new FilterRegistrationBean<>();

        OncePerRequestFilter contentCachingFilter = new OncePerRequestFilter() {
            @Override
            protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
                    throws ServletException, IOException {

                ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
                ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);

                try {
                    filterChain.doFilter(requestWrapper, responseWrapper);
                } finally {
                    // 不在这里复制响应体，而是在拦截器的afterCompletion方法中进行
                    // 这样可以确保在记录日志后再复制响应体
                }
            }
        };

        registrationBean.setFilter(contentCachingFilter);
        registrationBean.addUrlPatterns("/*");
        registrationBean.setName("contentCachingFilter");
        registrationBean.setOrder(1);

        return registrationBean;
    }
}
