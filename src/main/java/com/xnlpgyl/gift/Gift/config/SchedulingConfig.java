package com.xnlpgyl.gift.Gift.config;
import com.xnlpgyl.gift.Gift.User.service.impl.UserServiceImpl;
import org.springframework.scheduling.annotation.Scheduled;

import org.springframework.context.annotation.Configuration;

/**
 * 定时任务
 */
@Configuration
public class SchedulingConfig {
    private static final long ONE_HOUR = 3600000;
    private static final long ONE_HALF_HOUR = 1800000;
    /**
     * 定时清理过期的登录失败记录
     */
    @Scheduled(fixedRate = ONE_HALF_HOUR)// 每30分钟执行一次
    public void cleanExpiredLoginFailRecords() {
        long now = System.currentTimeMillis();
        // 清理 loginFailMap 中超过一小时的记录
        UserServiceImpl.loginFailMap.forEach((username, failCount) -> {
            Long errorTime = UserServiceImpl.loginErrorTimeMap.get(username);
            if (errorTime != null && now - errorTime > ONE_HOUR) { // 超过一小时
                UserServiceImpl.loginFailMap.remove(username);
                UserServiceImpl.loginErrorTimeMap.remove(username);
            }
        });
        // 清理 lockMap 中超过一小时的记录
        UserServiceImpl.lockMap.forEach((username, lockTime) -> {
            if (now - lockTime > 0) { // 超过一小时 解锁
                UserServiceImpl.lockMap.remove(username);
            }
        });

        // 清理 captchaFailMap 中超过一小时的记录
        UserServiceImpl.captchaFailMap.forEach((username, failCount) -> {
            Long errorTime = UserServiceImpl.captchaErrorTimeMap.get(username);
            if (errorTime != null && now - errorTime > ONE_HOUR) { // 超过一小时
                UserServiceImpl.captchaFailMap.remove(username);
                UserServiceImpl.captchaErrorTimeMap.remove(username);
            }
        });
    }
}
