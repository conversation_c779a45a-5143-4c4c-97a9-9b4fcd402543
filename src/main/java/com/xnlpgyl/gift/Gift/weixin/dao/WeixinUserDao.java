package com.xnlpgyl.gift.Gift.weixin.dao;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.BaseDao;
import com.xnlpgyl.gift.Gift.weixin.entity.WeixinUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 微信用户数据访问层
 * 
 * <AUTHOR>
 * @since 2025-06-29
 */
@Repository
public class WeixinUserDao extends BaseDao {
    
    /**
     * 微信用户集合名称
     */
    public static final String COLLECTION_NAME_WEIXIN_USER = "weixinUser";
    
    @Autowired
    private MongoTemplate mongoTemplate;
    
    /**
     * 根据OpenID查找微信用户
     * 
     * @param openId 微信用户OpenID
     * @return 微信用户信息
     */
    public WeixinUser findByOpenId(String openId) {
        Query query = new Query(Criteria.where("openId").is(openId));
        return mongoTemplate.findOne(query, WeixinUser.class, COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 根据UnionID查找微信用户
     * 
     * @param unionId 微信用户UnionID
     * @return 微信用户信息
     */
    public WeixinUser findByUnionId(String unionId) {
        Query query = new Query(Criteria.where("unionId").is(unionId));
        return mongoTemplate.findOne(query, WeixinUser.class, COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 保存或更新微信用户信息
     * 
     * @param weixinUser 微信用户信息
     * @return 保存后的用户信息
     */
    public WeixinUser saveOrUpdate(WeixinUser weixinUser) {
        if (weixinUser.getOpenId() == null) {
            throw new IllegalArgumentException("OpenID不能为空");
        }
        
        WeixinUser existingUser = findByOpenId(weixinUser.getOpenId());
        if (existingUser != null) {
            // 更新现有用户信息
            weixinUser.setCreateTime(existingUser.getCreateTime());
            weixinUser.setLoginCount(existingUser.getLoginCount());
        }
        
        weixinUser.setUpdateTime(LocalDateTime.now());
        return mongoTemplate.save(weixinUser, COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 更新用户登录信息
     * 
     * @param openId 用户OpenID
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn 过期时间
     */
    public void updateLoginInfo(String openId, String accessToken, String refreshToken, Integer expiresIn) {
        Query query = new Query(Criteria.where("openId").is(openId));
        Update update = new Update()
                .set("accessToken", accessToken)
                .set("refreshToken", refreshToken)
                .set("expiresIn", expiresIn)
                .set("lastLoginTime", LocalDateTime.now())
                .set("updateTime", LocalDateTime.now())
                .inc("loginCount", 1);
        
        mongoTemplate.updateFirst(query, update, COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 根据OpenID删除用户
     * 
     * @param openId 用户OpenID
     * @return 是否删除成功
     */
    public boolean deleteByOpenId(String openId) {
        Query query = new Query(Criteria.where("openId").is(openId));
        return mongoTemplate.remove(query, COLLECTION_NAME_WEIXIN_USER).getDeletedCount() > 0;
    }
    
    /**
     * 启用或禁用用户
     * 
     * @param openId 用户OpenID
     * @param enabled 是否启用
     */
    public void updateUserStatus(String openId, boolean enabled) {
        Query query = new Query(Criteria.where("openId").is(openId));
        Update update = new Update()
                .set("enabled", enabled)
                .set("updateTime", LocalDateTime.now());
        
        mongoTemplate.updateFirst(query, update, COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 查询所有启用的用户
     * 
     * @return 启用的用户列表
     */
    public List<WeixinUser> findEnabledUsers() {
        Query query = new Query(Criteria.where("enabled").is(true));
        return mongoTemplate.find(query, WeixinUser.class, COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 根据昵称模糊查询用户
     * 
     * @param nickname 昵称关键字
     * @return 匹配的用户列表
     */
    public List<WeixinUser> findByNicknameLike(String nickname) {
        Query query = new Query(Criteria.where("nickname").regex(nickname, "i"));
        return mongoTemplate.find(query, WeixinUser.class, COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 统计用户总数
     * 
     * @return 用户总数
     */
    public long countUsers() {
        return mongoTemplate.count(new Query(), COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 统计启用用户数
     * 
     * @return 启用用户数
     */
    public long countEnabledUsers() {
        Query query = new Query(Criteria.where("enabled").is(true));
        return mongoTemplate.count(query, COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 查询指定时间范围内的新用户
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 新用户列表
     */
    public List<WeixinUser> findNewUsers(LocalDateTime startTime, LocalDateTime endTime) {
        Query query = new Query(Criteria.where("createTime").gte(startTime).lte(endTime));
        return mongoTemplate.find(query, WeixinUser.class, COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 查询活跃用户（指定时间范围内有登录记录）
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 活跃用户列表
     */
    public List<WeixinUser> findActiveUsers(LocalDateTime startTime, LocalDateTime endTime) {
        Query query = new Query(Criteria.where("lastLoginTime").gte(startTime).lte(endTime));
        return mongoTemplate.find(query, WeixinUser.class, COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 分页查询用户
     * 
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 用户列表
     */
    public List<WeixinUser> findUsersWithPagination(int page, int size) {
        Query query = new Query().skip(page * size).limit(size);
        return mongoTemplate.find(query, WeixinUser.class, COLLECTION_NAME_WEIXIN_USER);
    }
    
    /**
     * 清理过期的访问令牌
     * 清理超过指定天数未登录的用户的访问令牌
     * 
     * @param days 天数
     */
    public void cleanExpiredTokens(int days) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
        Query query = new Query(Criteria.where("lastLoginTime").lt(expireTime));
        Update update = new Update()
                .unset("accessToken")
                .unset("refreshToken")
                .set("updateTime", LocalDateTime.now());
        
        mongoTemplate.updateMulti(query, update, COLLECTION_NAME_WEIXIN_USER);
    }
}
