package com.xnlpgyl.gift.Gift.weixin.dto;

import jakarta.validation.constraints.NotBlank;

/**
 * 微信小程序登录请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
public class WeixinLoginRequest {

    /**
     * 小程序登录凭证（通过wx.login获取）
     */
    @NotBlank(message = "登录凭证不能为空")
    private String code;
    
    /**
     * 用户手机号授权码（可选，用于获取手机号）
     */
    private String phoneCode;

    /**
     * 客户端类型（miniapp）
     */
    private String clientType = "miniapp";

    /**
     * 设备信息
     */
    private String deviceInfo;
    
    public WeixinLoginRequest() {
    }
    
    public WeixinLoginRequest(String code, String phoneCode) {
        this.code = code;
        this.phoneCode = phoneCode;
    }
    
    // Getters and Setters
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getPhoneCode() {
        return phoneCode;
    }

    public void setPhoneCode(String phoneCode) {
        this.phoneCode = phoneCode;
    }
    
    public String getClientType() {
        return clientType;
    }
    
    public void setClientType(String clientType) {
        this.clientType = clientType;
    }
    
    public String getDeviceInfo() {
        return deviceInfo;
    }
    
    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }
    
    @Override
    public String toString() {
        return "WeixinLoginRequest{" +
                "code='" + code + '\'' +
                ", phoneCode='" + phoneCode + '\'' +
                ", clientType='" + clientType + '\'' +
                ", deviceInfo='" + deviceInfo + '\'' +
                '}';
    }
}
