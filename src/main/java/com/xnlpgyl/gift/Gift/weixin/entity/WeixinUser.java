package com.xnlpgyl.gift.Gift.weixin.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 微信用户信息实体类
 * 用于存储从微信OAuth2.0接口获取的用户信息
 * 
 * <AUTHOR>
 * @since 2025-06-29
 */
public class WeixinUser implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户的唯一标识
     */
    @JsonProperty("openid")
    @JSONField(name = "openid")
    private String openId;
    
    /**
     * 用户昵称
     */
    @JsonProperty("nickname")
    @JSONField(name = "nickname")
    private String nickname;
    
    /**
     * 用户的性别，值为1时是男性，值为2时是女性，值为0时是未知
     */
    @JsonProperty("sex")
    @JSONField(name = "sex")
    private Integer sex;
    
    /**
     * 用户个人资料填写的省份
     */
    @JsonProperty("province")
    @JSONField(name = "province")
    private String province;
    
    /**
     * 普通用户个人资料填写的城市
     */
    @JsonProperty("city")
    @JSONField(name = "city")
    private String city;
    
    /**
     * 国家，如中国为CN
     */
    @JsonProperty("country")
    @JSONField(name = "country")
    private String country;
    
    /**
     * 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），
     * 用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。
     */
    @JsonProperty("headimgurl")
    @JSONField(name = "headimgurl")
    private String headImgUrl;
    
    /**
     * 用户特权信息，json 数组，如微信沃卡用户为（chinaunicom）
     */
    @JsonProperty("privilege")
    @JSONField(name = "privilege")
    private String[] privilege;
    
    /**
     * 只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。
     */
    @JsonProperty("unionid")
    @JSONField(name = "unionid")
    private String unionId;
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 刷新令牌
     */
    private String refreshToken;
    
    /**
     * 访问令牌过期时间（秒）
     */
    private Integer expiresIn;
    
    /**
     * 授权作用域
     */
    private String scope;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 登录次数
     */
    private Integer loginCount;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    // 构造函数
    public WeixinUser() {
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.loginCount = 0;
        this.enabled = true;
    }
    
    // Getters and Setters
    public String getOpenId() {
        return openId;
    }
    
    public void setOpenId(String openId) {
        this.openId = openId;
    }
    
    public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
    
    public Integer getSex() {
        return sex;
    }
    
    public void setSex(Integer sex) {
        this.sex = sex;
    }
    
    public String getProvince() {
        return province;
    }
    
    public void setProvince(String province) {
        this.province = province;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
    
    public String getHeadImgUrl() {
        return headImgUrl;
    }
    
    public void setHeadImgUrl(String headImgUrl) {
        this.headImgUrl = headImgUrl;
    }
    
    public String[] getPrivilege() {
        return privilege;
    }
    
    public void setPrivilege(String[] privilege) {
        this.privilege = privilege;
    }
    
    public String getUnionId() {
        return unionId;
    }
    
    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }
    
    public String getAccessToken() {
        return accessToken;
    }
    
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
    
    public String getRefreshToken() {
        return refreshToken;
    }
    
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }
    
    public Integer getExpiresIn() {
        return expiresIn;
    }
    
    public void setExpiresIn(Integer expiresIn) {
        this.expiresIn = expiresIn;
    }
    
    public String getScope() {
        return scope;
    }
    
    public void setScope(String scope) {
        this.scope = scope;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public LocalDateTime getLastLoginTime() {
        return lastLoginTime;
    }
    
    public void setLastLoginTime(LocalDateTime lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }
    
    public Integer getLoginCount() {
        return loginCount;
    }
    
    public void setLoginCount(Integer loginCount) {
        this.loginCount = loginCount;
    }
    
    public Boolean getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    
    /**
     * 获取性别描述
     * 
     * @return 性别描述
     */
    public String getSexDesc() {
        if (sex == null) {
            return "未知";
        }
        switch (sex) {
            case 1:
                return "男";
            case 2:
                return "女";
            default:
                return "未知";
        }
    }
    
    /**
     * 更新登录信息
     */
    public void updateLoginInfo() {
        this.lastLoginTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.loginCount = this.loginCount == null ? 1 : this.loginCount + 1;
    }
    
    @Override
    public String toString() {
        return "WeixinUser{" +
                "openId='" + openId + '\'' +
                ", nickname='" + nickname + '\'' +
                ", sex=" + sex +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", country='" + country + '\'' +
                ", headImgUrl='" + headImgUrl + '\'' +
                ", unionId='" + unionId + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", lastLoginTime=" + lastLoginTime +
                ", loginCount=" + loginCount +
                ", enabled=" + enabled +
                '}';
    }
}
