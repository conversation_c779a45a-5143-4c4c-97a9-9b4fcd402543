package com.xnlpgyl.gift.Gift.weixin.service;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.User.entity.User;
import com.xnlpgyl.gift.Gift.User.service.IUserService;
import com.xnlpgyl.gift.Gift.security.LoginUser;
import com.xnlpgyl.gift.Gift.utils.JwtUtil;
import com.xnlpgyl.gift.Gift.weixin.config.WeixinMiniAppConfig;
import com.xnlpgyl.gift.Gift.weixin.dao.WeixinUserDao;
import com.xnlpgyl.gift.Gift.weixin.dto.WeixinLoginRequest;
import com.xnlpgyl.gift.Gift.weixin.dto.WeixinLoginResponse;
import com.xnlpgyl.gift.Gift.weixin.entity.WeixinUser;
import com.xnlpgyl.gift.Gift.wxminiapp.OkHttpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 微信小程序登录服务类
 * 处理微信小程序登录相关业务逻辑
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@Service
@Transactional
public class WeixinOAuth2Service {

    private static final Logger logger = LoggerFactory.getLogger(WeixinOAuth2Service.class);

    @Autowired
    private WeixinMiniAppConfig weixinConfig;
    
    @Autowired
    private WeixinUserDao weixinUserDao;
    
    @Autowired
    private OkHttpService okHttpService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private IUserService userService;
    
    @Value("${jwt.expiration}")
    private Long jwtExpiration;
    
    /**
     * 获取小程序AppID（前端wx.login需要）
     *
     * @return 小程序AppID
     */
    public String getAppId() {
        return weixinConfig.getAppId();
    }
    
    /**
     * 处理微信小程序登录，完成用户登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    public WeixinLoginResponse handleMiniAppLogin(WeixinLoginRequest request) {
        try {
            logger.info("开始处理微信小程序登录，code: {}", request.getCode());

            // 1. 通过code获取session_key和openid
            JSONObject sessionResponse = getSessionKey(request.getCode());
            if (sessionResponse == null || sessionResponse.containsKey("errcode")) {
                logger.error("获取session_key失败: {}", sessionResponse);
                String errorMsg = sessionResponse != null ? sessionResponse.getString("errmsg") : "未知错误";
                return WeixinLoginResponse.failure("小程序登录失败：" + errorMsg);
            }

            String openId = sessionResponse.getString("openid");
            String sessionKey = sessionResponse.getString("session_key");
            String unionId = sessionResponse.getString("unionid"); // 可能为空
            
            // 2. 创建或获取微信用户信息
            WeixinUser weixinUser = createOrGetWeixinUser(openId, unionId, sessionKey);
            if (weixinUser == null) {
                logger.error("创建微信用户失败，openId: {}", openId);
                return WeixinLoginResponse.failure("创建用户信息失败");
            }
            
            // 3. 检查是否为新用户
            WeixinUser existingUser = weixinUserDao.findByOpenId(openId);
            boolean isNewUser = existingUser == null;

            // 4. 保存或更新用户信息
            if (isNewUser) {
                weixinUser.setCreateTime(LocalDateTime.now());
                weixinUser.setLoginCount(1);
            } else {
                weixinUser.setCreateTime(existingUser.getCreateTime());
                weixinUser.setLoginCount(existingUser.getLoginCount() + 1);
                // 保留原有的昵称等信息（小程序登录时可能没有用户信息）
                if (existingUser.getNickname() != null) {
                    weixinUser.setNickname(existingUser.getNickname());
                }
                if (existingUser.getHeadImgUrl() != null) {
                    weixinUser.setHeadImgUrl(existingUser.getHeadImgUrl());
                }
            }
            weixinUser.updateLoginInfo();
            weixinUserDao.saveOrUpdate(weixinUser);

            // 5. 创建或关联系统用户
            User systemUser = createOrGetSystemUser(weixinUser);

            // 6. 生成JWT令牌
            LoginUser loginUser = new LoginUser(systemUser);
            String jwtToken = jwtUtil.generateToken(loginUser);

            logger.info("微信小程序用户登录成功，openId: {}, isNewUser: {}", openId, isNewUser);

            return WeixinLoginResponse.success(jwtToken, jwtExpiration * 1000, weixinUser, isNewUser);
            
        } catch (Exception e) {
            logger.error("处理微信小程序登录时发生异常", e);
            return WeixinLoginResponse.failure("登录处理异常：" + e.getMessage());
        }
    }
    
    /**
     * 通过小程序登录凭证获取session_key和openid
     *
     * @param jsCode 小程序登录凭证
     * @return session响应
     */
    private JSONObject getSessionKey(String jsCode) {
        try {
            String url = weixinConfig.buildLoginUrl(jsCode);
            String response = okHttpService.get(url);
            return JSONObject.parseObject(response);
        } catch (Exception e) {
            logger.error("获取session_key时发生异常", e);
            return null;
        }
    }
    
    /**
     * 创建或获取微信用户信息
     *
     * @param openId 用户openid
     * @param unionId 用户unionid（可能为空）
     * @param sessionKey session_key
     * @return 微信用户信息
     */
    private WeixinUser createOrGetWeixinUser(String openId, String unionId, String sessionKey) {
        try {
            WeixinUser weixinUser = new WeixinUser();
            weixinUser.setOpenId(openId);
            weixinUser.setUnionId(unionId);

            // 小程序登录时，用户信息需要通过其他方式获取（如用户授权）
            // 这里先设置基本信息，昵称和头像可以后续通过用户授权获取
            weixinUser.setNickname("小程序用户"); // 默认昵称
            weixinUser.setScope("miniapp_login");

            return weixinUser;
        } catch (Exception e) {
            logger.error("创建微信用户信息时发生异常", e);
            return null;
        }
    }
    
    /**
     * 创建或获取系统用户
     * 
     * @param weixinUser 微信用户
     * @return 系统用户
     */
    private User createOrGetSystemUser(WeixinUser weixinUser) {
        // 尝试通过微信openId查找现有系统用户
        User existingUser = userService.lambdaQuery()
                .eq(User::getUsername, "wx_" + weixinUser.getOpenId())
                .one();
        
        if (existingUser != null) {
            // 更新用户信息
            existingUser.setNickname(weixinUser.getNickname());
            userService.updateById(existingUser);
            return existingUser;
        }
        
        // 创建新的系统用户
        User newUser = new User();
        newUser.setUsername("wx_" + weixinUser.getOpenId());
        newUser.setNickname(weixinUser.getNickname());
        newUser.setPassword(""); // 微信用户不需要密码
        newUser.setEmail(""); // 微信用户可能没有邮箱
        newUser.setPhone(""); // 微信用户可能没有手机号
        
        userService.save(newUser);
        return newUser;
    }
    
    /**
     * 获取小程序全局access_token（用于调用其他微信API）
     *
     * @return access_token
     */
    public String getGlobalAccessToken() {
        try {
            String url = weixinConfig.buildAccessTokenUrl();
            String response = okHttpService.get(url);
            JSONObject result = JSONObject.parseObject(response);
            if (result.containsKey("access_token")) {
                return result.getString("access_token");
            }
            logger.error("获取全局access_token失败: {}", result);
            return null;
        } catch (Exception e) {
            logger.error("获取全局access_token时发生异常", e);
            return null;
        }
    }
    
    /**
     * 根据OpenID获取微信用户信息
     * 
     * @param openId 用户OpenID
     * @return 微信用户信息
     */
    @Transactional(readOnly = true)
    public WeixinUser getWeixinUserByOpenId(String openId) {
        return weixinUserDao.findByOpenId(openId);
    }
    
    /**
     * 根据UnionID获取微信用户信息
     * 
     * @param unionId 用户UnionID
     * @return 微信用户信息
     */
    @Transactional(readOnly = true)
    public WeixinUser getWeixinUserByUnionId(String unionId) {
        return weixinUserDao.findByUnionId(unionId);
    }
    
    /**
     * 禁用微信用户
     * 
     * @param openId 用户OpenID
     */
    public void disableUser(String openId) {
        weixinUserDao.updateUserStatus(openId, false);
        logger.info("已禁用微信用户，openId: {}", openId);
    }
    
    /**
     * 启用微信用户
     * 
     * @param openId 用户OpenID
     */
    public void enableUser(String openId) {
        weixinUserDao.updateUserStatus(openId, true);
        logger.info("已启用微信用户，openId: {}", openId);
    }
}
