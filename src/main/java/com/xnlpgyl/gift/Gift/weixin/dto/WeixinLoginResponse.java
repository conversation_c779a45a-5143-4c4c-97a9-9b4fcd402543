package com.xnlpgyl.gift.Gift.weixin.dto;

import com.xnlpgyl.gift.Gift.weixin.entity.WeixinUser;

/**
 * 微信登录响应DTO
 * 
 * <AUTHOR>
 * @since 2025-06-29
 */
public class WeixinLoginResponse {
    
    /**
     * 是否登录成功
     */
    private Boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * JWT访问令牌
     */
    private String accessToken;
    
    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";
    
    /**
     * 令牌过期时间（毫秒）
     */
    private Long expiresIn;
    
    /**
     * 微信用户信息
     */
    private WeixinUser userInfo;
    
    /**
     * 是否为新用户
     */
    private Boolean isNewUser;
    
    /**
     * 登录时间戳
     */
    private Long loginTime;
    
    public WeixinLoginResponse() {
        this.loginTime = System.currentTimeMillis();
    }
    
    public WeixinLoginResponse(Boolean success, String message) {
        this();
        this.success = success;
        this.message = message;
    }
    
    /**
     * 创建成功响应
     * 
     * @param accessToken JWT访问令牌
     * @param expiresIn 过期时间
     * @param userInfo 用户信息
     * @param isNewUser 是否为新用户
     * @return 成功响应
     */
    public static WeixinLoginResponse success(String accessToken, Long expiresIn, WeixinUser userInfo, Boolean isNewUser) {
        WeixinLoginResponse response = new WeixinLoginResponse(true, "登录成功");
        response.setAccessToken(accessToken);
        response.setExpiresIn(expiresIn);
        response.setUserInfo(userInfo);
        response.setIsNewUser(isNewUser);
        return response;
    }
    
    /**
     * 创建失败响应
     * 
     * @param message 错误消息
     * @return 失败响应
     */
    public static WeixinLoginResponse failure(String message) {
        return new WeixinLoginResponse(false, message);
    }
    
    // Getters and Setters
    public Boolean getSuccess() {
        return success;
    }
    
    public void setSuccess(Boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getAccessToken() {
        return accessToken;
    }
    
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
    
    public String getTokenType() {
        return tokenType;
    }
    
    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }
    
    public Long getExpiresIn() {
        return expiresIn;
    }
    
    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }
    
    public WeixinUser getUserInfo() {
        return userInfo;
    }
    
    public void setUserInfo(WeixinUser userInfo) {
        this.userInfo = userInfo;
    }
    
    public Boolean getIsNewUser() {
        return isNewUser;
    }
    
    public void setIsNewUser(Boolean isNewUser) {
        this.isNewUser = isNewUser;
    }
    
    public Long getLoginTime() {
        return loginTime;
    }
    
    public void setLoginTime(Long loginTime) {
        this.loginTime = loginTime;
    }
    
    @Override
    public String toString() {
        return "WeixinLoginResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", accessToken='" + accessToken + '\'' +
                ", tokenType='" + tokenType + '\'' +
                ", expiresIn=" + expiresIn +
                ", userInfo=" + userInfo +
                ", isNewUser=" + isNewUser +
                ", loginTime=" + loginTime +
                '}';
    }
}
