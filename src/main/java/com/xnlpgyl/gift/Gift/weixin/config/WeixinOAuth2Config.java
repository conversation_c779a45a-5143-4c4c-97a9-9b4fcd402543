package com.xnlpgyl.gift.Gift.weixin.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信小程序登录配置类
 * 用于配置微信小程序登录参数
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@Configuration
@ConfigurationProperties(prefix = "weixin.miniapp")
public class WeixinOAuth2Config {
    
    /**
     * 微信公众号/网页应用的AppID（用于OAuth2.0网页授权）
     * 注意：这里不能使用小程序的AppID，需要使用公众号的AppID
     */
    private String appId = "wx01439c716224ea66";

    /**
     * 微信公众号/网页应用的AppSecret（用于OAuth2.0网页授权）
     * 注意：这里不能使用小程序的AppSecret，需要使用公众号的AppSecret
     */
    private String appSecret = "60597d7a00c56aa881f24a936b0b54ab";

    /**
     * 微信小程序的AppID（如果需要同时支持小程序登录）
     */
    private String miniAppId = "wxae95026f0450c13e";

    /**
     * 微信小程序的AppSecret（如果需要同时支持小程序登录）
     */
    private String miniAppSecret = "d09c8a47f3dd366f13452e23735337d8";
    
    /**
     * 微信OAuth2.0授权URL
     */
    private String authorizeUrl = "https://open.weixin.qq.com/connect/oauth2/authorize";
    
    /**
     * 微信获取access_token的URL
     */
    private String accessTokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token";
    
    /**
     * 微信获取用户信息的URL
     */
    private String userInfoUrl = "https://api.weixin.qq.com/sns/userinfo";
    
    /**
     * 微信刷新access_token的URL
     */
    private String refreshTokenUrl = "https://api.weixin.qq.com/sns/oauth2/refresh_token";
    
    /**
     * 微信验证access_token有效性的URL
     */
    private String validateTokenUrl = "https://api.weixin.qq.com/sns/auth";
    
    /**
     * 授权回调地址
     */
    private String redirectUri = "https://www.xnlpgyl.com/api/gift/weixin/oauth2/callback";
    
    /**
     * 授权作用域，snsapi_base（不弹出授权页面，直接跳转，只能获取用户openid），
     * snsapi_userinfo（弹出授权页面，可通过openid拿到昵称、性别、所在地）
     */
    private String scope = "snsapi_userinfo";
    
    /**
     * 状态参数，用于防止CSRF攻击
     */
    private String state = "STATE";
    
    // Getters and Setters
    public String getAppId() {
        return appId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getAppSecret() {
        return appSecret;
    }
    
    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }
    
    public String getAuthorizeUrl() {
        return authorizeUrl;
    }
    
    public void setAuthorizeUrl(String authorizeUrl) {
        this.authorizeUrl = authorizeUrl;
    }
    
    public String getAccessTokenUrl() {
        return accessTokenUrl;
    }
    
    public void setAccessTokenUrl(String accessTokenUrl) {
        this.accessTokenUrl = accessTokenUrl;
    }
    
    public String getUserInfoUrl() {
        return userInfoUrl;
    }
    
    public void setUserInfoUrl(String userInfoUrl) {
        this.userInfoUrl = userInfoUrl;
    }
    
    public String getRefreshTokenUrl() {
        return refreshTokenUrl;
    }
    
    public void setRefreshTokenUrl(String refreshTokenUrl) {
        this.refreshTokenUrl = refreshTokenUrl;
    }
    
    public String getValidateTokenUrl() {
        return validateTokenUrl;
    }
    
    public void setValidateTokenUrl(String validateTokenUrl) {
        this.validateTokenUrl = validateTokenUrl;
    }
    
    public String getRedirectUri() {
        return redirectUri;
    }
    
    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }
    
    public String getScope() {
        return scope;
    }
    
    public void setScope(String scope) {
        this.scope = scope;
    }
    
    public String getState() {
        return state;
    }
    
    public void setState(String state) {
        this.state = state;
    }

    public String getMiniAppId() {
        return miniAppId;
    }

    public void setMiniAppId(String miniAppId) {
        this.miniAppId = miniAppId;
    }

    public String getMiniAppSecret() {
        return miniAppSecret;
    }

    public void setMiniAppSecret(String miniAppSecret) {
        this.miniAppSecret = miniAppSecret;
    }
    
    /**
     * 构建微信OAuth2.0授权URL
     * 
     * @return 完整的授权URL
     */
    public String buildAuthorizeUrl() {
        return String.format("%s?appid=%s&redirect_uri=%s&response_type=code&scope=%s&state=%s#wechat_redirect",
                authorizeUrl, appId, redirectUri, scope, state);
    }
    
    /**
     * 构建获取access_token的URL
     * 
     * @param code 授权码
     * @return 完整的获取access_token的URL
     */
    public String buildAccessTokenUrl(String code) {
        return String.format("%s?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
                accessTokenUrl, appId, appSecret, code);
    }
    
    /**
     * 构建获取用户信息的URL
     * 
     * @param accessToken 访问令牌
     * @param openId 用户openid
     * @return 完整的获取用户信息的URL
     */
    public String buildUserInfoUrl(String accessToken, String openId) {
        return String.format("%s?access_token=%s&openid=%s&lang=zh_CN",
                userInfoUrl, accessToken, openId);
    }
    
    /**
     * 构建刷新access_token的URL
     * 
     * @param refreshToken 刷新令牌
     * @return 完整的刷新access_token的URL
     */
    public String buildRefreshTokenUrl(String refreshToken) {
        return String.format("%s?appid=%s&grant_type=refresh_token&refresh_token=%s",
                refreshTokenUrl, appId, refreshToken);
    }
    
    /**
     * 构建验证access_token有效性的URL
     * 
     * @param accessToken 访问令牌
     * @param openId 用户openid
     * @return 完整的验证URL
     */
    public String buildValidateTokenUrl(String accessToken, String openId) {
        return String.format("%s?access_token=%s&openid=%s",
                validateTokenUrl, accessToken, openId);
    }
}
