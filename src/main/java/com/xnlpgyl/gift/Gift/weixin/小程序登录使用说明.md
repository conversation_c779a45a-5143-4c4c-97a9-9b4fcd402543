# 微信小程序登录使用说明

## 🎉 已完成的修改

系统已成功从微信网页OAuth2.0登录改为微信小程序登录，现在可以使用您的认证小程序AppID和AppSecret进行登录。

## 📋 当前配置

### 小程序信息
- **AppID**: `wxae95026f0450c13e`
- **AppSecret**: `d09c8a47f3dd366f13452e23735337d8`
- **登录方式**: 微信小程序登录（wx.login）

### API接口地址
- **基础路径**: `/weixin/miniapp`
- **登录接口**: `POST /weixin/miniapp/login`
- **获取AppID**: `GET /weixin/miniapp/app-id`
- **获取配置**: `GET /weixin/miniapp/config`

## 🚀 小程序端集成

### 1. 小程序登录流程

```javascript
// 小程序端代码示例
Page({
  data: {
    userInfo: null,
    token: null
  },

  // 微信登录
  wxLogin() {
    wx.showLoading({
      title: '登录中...'
    });

    // 1. 调用wx.login获取code
    wx.login({
      success: (res) => {
        if (res.code) {
          // 2. 将code发送到后端
          this.sendCodeToServer(res.code);
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '登录失败',
            icon: 'error'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '登录失败',
          icon: 'error'
        });
      }
    });
  },

  // 发送code到服务器
  sendCodeToServer(code) {
    wx.request({
      url: 'https://www.xnlpgyl.com/api/gift/weixin/miniapp/login',
      method: 'POST',
      header: {
        'content-type': 'application/json'
      },
      data: {
        code: code,
        clientType: 'miniapp',
        deviceInfo: wx.getSystemInfoSync().model
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.data.code === 200 && res.data.data.success) {
          // 登录成功
          const loginData = res.data.data;
          
          // 保存token
          wx.setStorageSync('token', loginData.accessToken);
          wx.setStorageSync('userInfo', loginData.userInfo);
          
          this.setData({
            token: loginData.accessToken,
            userInfo: loginData.userInfo
          });

          wx.showToast({
            title: loginData.isNewUser ? '欢迎新用户!' : '登录成功',
            icon: 'success'
          });

          // 跳转到主页面
          wx.switchTab({
            url: '/pages/index/index'
          });
        } else {
          wx.showToast({
            title: res.data.message || '登录失败',
            icon: 'error'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误',
          icon: 'error'
        });
      }
    });
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.setData({
        token: token,
        userInfo: userInfo
      });
      return true;
    }
    return false;
  },

  // 退出登录
  logout() {
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    this.setData({
      token: null,
      userInfo: null
    });
    
    wx.showToast({
      title: '已退出登录',
      icon: 'success'
    });
  }
});
```

### 2. 小程序页面示例

```xml
<!-- login.wxml -->
<view class="login-container">
  <view class="logo">
    <image src="/images/logo.png" mode="aspectFit"></image>
  </view>
  
  <view class="title">欢迎使用礼品系统</view>
  
  <view class="login-section" wx:if="{{!userInfo}}">
    <button class="login-btn" bindtap="wxLogin">
      <image class="wx-icon" src="/images/wechat.png"></image>
      微信登录
    </button>
  </view>
  
  <view class="user-section" wx:else>
    <view class="user-info">
      <image class="avatar" src="{{userInfo.headImgUrl || '/images/default-avatar.png'}}"></image>
      <text class="nickname">{{userInfo.nickname || '小程序用户'}}</text>
    </view>
    
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>
</view>
```

### 3. 请求拦截器（带token）

```javascript
// utils/request.js
const request = (options) => {
  const token = wx.getStorageSync('token');
  
  return new Promise((resolve, reject) => {
    wx.request({
      url: options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'content-type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // token过期，重新登录
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          wx.navigateTo({
            url: '/pages/login/login'
          });
          reject(new Error('登录已过期'));
        } else {
          reject(new Error(res.data.message || '请求失败'));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

module.exports = {
  request
};
```

## 🔧 后端API说明

### 1. 登录接口

**接口地址**: `POST /weixin/miniapp/login`

**请求参数**:
```json
{
  "code": "小程序登录凭证",
  "phoneCode": "手机号授权码（可选）",
  "clientType": "miniapp",
  "deviceInfo": "设备信息"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "message": "登录成功",
    "accessToken": "eyJhbGciOiJIUzI1NiJ9...",
    "tokenType": "Bearer",
    "expiresIn": 259200000,
    "userInfo": {
      "openId": "oxxxxxxxxxxxxxxxxxxxxxx",
      "nickname": "小程序用户",
      "unionId": "unionid_xxx",
      "createTime": "2025-06-29T19:00:00",
      "updateTime": "2025-06-29T19:00:00",
      "lastLoginTime": "2025-06-29T19:00:00",
      "loginCount": 1,
      "enabled": true
    },
    "isNewUser": true,
    "loginTime": 1719648600000
  }
}
```

### 2. 获取配置接口

**接口地址**: `GET /weixin/miniapp/config`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "appId": "wxae95026f0450c13e",
    "loginType": "miniapp",
    "version": "1.0.0"
  }
}
```

## 📝 注意事项

### 1. 小程序配置
- 确保小程序已发布并通过审核
- 在小程序后台配置服务器域名：`https://www.xnlpgyl.com`
- 确保AppID和AppSecret正确配置

### 2. 用户信息获取
- 小程序登录只能获取openid和unionid
- 用户昵称和头像需要通过用户授权获取
- 系统会自动创建用户名为 `wx_{openId}` 的系统用户

### 3. 安全考虑
- JWT令牌有效期为3天
- 支持用户启用/禁用功能
- 所有数据库操作都在事务中执行

## 🎯 测试步骤

1. **编译项目**: `mvn compile` ✅
2. **启动服务**: `mvn spring-boot:run`
3. **测试接口**: 
   - 获取配置: `GET /weixin/miniapp/config`
   - 模拟登录: `POST /weixin/miniapp/login`
4. **小程序集成**: 在小程序中集成登录代码
5. **功能测试**: 测试登录、退出、token验证等功能

## 🔄 与原有系统的区别

| 特性 | 网页OAuth2.0 | 小程序登录 |
|------|-------------|-----------|
| **登录方式** | 跳转授权页面 | wx.login() |
| **获取信息** | 完整用户信息 | openid + unionid |
| **接口路径** | `/weixin/oauth2/*` | `/weixin/miniapp/*` |
| **配置前缀** | `weixin.oauth2` | `weixin.miniapp` |
| **用户体验** | 需要跳转 | 无感登录 |

## ✅ 完成状态

- ✅ 配置类更新为小程序配置
- ✅ 服务类改为小程序登录逻辑
- ✅ 控制器接口更新
- ✅ 配置文件更新
- ✅ 编译测试通过
- ✅ 使用说明文档完成

现在您可以使用认证的小程序AppID和AppSecret进行小程序登录了！🎉
