package com.xnlpgyl.gift.Gift.weixin.controller;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.security.MyMenuCheck;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import com.xnlpgyl.gift.Gift.weixin.dto.WeixinLoginRequest;
import com.xnlpgyl.gift.Gift.weixin.dto.WeixinLoginResponse;
import com.xnlpgyl.gift.Gift.weixin.entity.WeixinUser;
import com.xnlpgyl.gift.Gift.weixin.service.WeixinOAuth2Service;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * 微信小程序登录控制器
 * 提供微信小程序登录相关接口
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@RestController
@RequestMapping("/weixin/miniapp")
@MyMenuCheck(auth = "weixin:miniapp:", desc = "微信小程序登录")
public class WeixinOAuth2Controller extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(WeixinOAuth2Controller.class);
    
    @Autowired
    private WeixinOAuth2Service weixinOAuth2Service;
    
    public WeixinOAuth2Controller(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }
    
    /**
     * 获取小程序AppID
     * 前端小程序可以通过此接口获取AppID用于wx.login
     *
     * @return 小程序AppID
     */
    @GetMapping("/app-id")
    public ResultMsg<String> getAppId() {
        try {
            String appId = weixinOAuth2Service.getAppId();
            logger.info("获取小程序AppID成功: {}", appId);
            return ResultMsg.success(appId);
        } catch (Exception e) {
            logger.error("获取小程序AppID失败", e);
            return ResultMsg.error("获取AppID失败：" + e.getMessage());
        }
    }
    
    /**
     * 微信小程序登录接口
     * 小程序通过wx.login获取code后调用此接口完成登录
     *
     * @param request 登录请求
     * @return 登录结果
     */
    @PostMapping("/login")
    public ResultMsg<WeixinLoginResponse> login(@Valid @RequestBody WeixinLoginRequest request) {
        try {
            logger.info("收到微信小程序登录请求: {}", request);

            WeixinLoginResponse response = weixinOAuth2Service.handleMiniAppLogin(request);

            if (response.getSuccess()) {
                logger.info("微信小程序登录成功，openId: {}", response.getUserInfo().getOpenId());
                return ResultMsg.success(response);
            } else {
                logger.warn("微信小程序登录失败: {}", response.getMessage());
                return ResultMsg.error(response.getMessage());
            }

        } catch (Exception e) {
            logger.error("处理微信小程序登录时发生异常", e);
            return ResultMsg.error("登录处理异常：" + e.getMessage());
        }
    }
    
    /**
     * 获取小程序登录配置信息
     *
     * @return 配置信息
     */
    @GetMapping("/config")
    public ResultMsg<JSONObject> getConfig() {
        try {
            JSONObject config = new JSONObject();
            config.put("appId", weixinOAuth2Service.getAppId());
            config.put("loginType", "miniapp");
            config.put("version", "1.0.0");

            return ResultMsg.success(config);
        } catch (Exception e) {
            logger.error("获取小程序配置失败", e);
            return ResultMsg.error("获取配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据OpenID获取微信用户信息
     * 
     * @param openId 微信用户OpenID
     * @return 用户信息
     */
    @GetMapping("/user/{openId}")
    public ResultMsg<WeixinUser> getUserByOpenId(@PathVariable String openId) {
        try {
            WeixinUser user = weixinOAuth2Service.getWeixinUserByOpenId(openId);
            if (user != null) {
                // 清除敏感信息
                user.setAccessToken(null);
                user.setRefreshToken(null);
                return ResultMsg.success(user);
            } else {
                return ResultMsg.error("用户不存在");
            }
        } catch (Exception e) {
            logger.error("获取微信用户信息时发生异常", e);
            return ResultMsg.error("获取用户信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 禁用微信用户
     * 
     * @param openId 微信用户OpenID
     * @return 操作结果
     */
    @PostMapping("/user/{openId}/disable")
    public ResultMsg<Void> disableUser(@PathVariable String openId) {
        try {
            weixinOAuth2Service.disableUser(openId);
            return ResultMsg.success();
        } catch (Exception e) {
            logger.error("禁用微信用户时发生异常", e);
            return ResultMsg.error("禁用用户失败：" + e.getMessage());
        }
    }
    
    /**
     * 启用微信用户
     * 
     * @param openId 微信用户OpenID
     * @return 操作结果
     */
    @PostMapping("/user/{openId}/enable")
    public ResultMsg<Void> enableUser(@PathVariable String openId) {
        try {
            weixinOAuth2Service.enableUser(openId);
            return ResultMsg.success();
        } catch (Exception e) {
            logger.error("启用微信用户时发生异常", e);
            return ResultMsg.error("启用用户失败：" + e.getMessage());
        }
    }
    

}
