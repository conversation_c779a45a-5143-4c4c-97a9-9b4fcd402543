package com.xnlpgyl.gift.Gift.weixin.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信小程序登录配置类
 * 用于配置微信小程序登录参数
 * 
 * <AUTHOR>
 * @since 2025-06-29
 */
@Configuration
@ConfigurationProperties(prefix = "weixin.miniapp")
public class WeixinMiniAppConfig {
    
    /**
     * 微信小程序的AppID
     */
    private String appId = "wxae95026f0450c13e";
    
    /**
     * 微信小程序的AppSecret
     */
    private String appSecret = "d09c8a47f3dd366f13452e23735337d8";
    
    /**
     * 微信小程序登录接口URL
     */
    private String loginUrl = "https://api.weixin.qq.com/sns/jscode2session";
    
    /**
     * 微信小程序获取access_token的URL
     */
    private String accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/token";
    
    /**
     * 微信小程序获取用户手机号的URL
     */
    private String phoneNumberUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber";
    
    /**
     * 授权类型
     */
    private String grantType = "authorization_code";
    
    // Getters and Setters
    public String getAppId() {
        return appId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getAppSecret() {
        return appSecret;
    }
    
    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }
    
    public String getLoginUrl() {
        return loginUrl;
    }
    
    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }
    
    public String getAccessTokenUrl() {
        return accessTokenUrl;
    }
    
    public void setAccessTokenUrl(String accessTokenUrl) {
        this.accessTokenUrl = accessTokenUrl;
    }
    
    public String getPhoneNumberUrl() {
        return phoneNumberUrl;
    }
    
    public void setPhoneNumberUrl(String phoneNumberUrl) {
        this.phoneNumberUrl = phoneNumberUrl;
    }
    
    public String getGrantType() {
        return grantType;
    }
    
    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }
    
    /**
     * 构建小程序登录URL
     * 
     * @param jsCode 小程序登录凭证
     * @return 完整的登录URL
     */
    public String buildLoginUrl(String jsCode) {
        return String.format("%s?appid=%s&secret=%s&js_code=%s&grant_type=%s",
                loginUrl, appId, appSecret, jsCode, grantType);
    }
    
    /**
     * 构建获取access_token的URL
     * 
     * @return 完整的获取access_token的URL
     */
    public String buildAccessTokenUrl() {
        return String.format("%s?appid=%s&secret=%s&grant_type=client_credential",
                accessTokenUrl, appId, appSecret);
    }
    
    /**
     * 构建获取用户手机号的URL
     * 
     * @param accessToken 访问令牌
     * @return 完整的获取手机号的URL
     */
    public String buildPhoneNumberUrl(String accessToken) {
        return String.format("%s?access_token=%s", phoneNumberUrl, accessToken);
    }
}
