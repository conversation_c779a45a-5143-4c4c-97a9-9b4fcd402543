package com.xnlpgyl.gift.Gift.diy.controller;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.diy.service.DiyPriceService;
import com.xnlpgyl.gift.Gift.security.MyAuthCheck;
import com.xnlpgyl.gift.Gift.security.MyMenuCheck;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * DIY价格策略管理
 */
@RestController
@RequestMapping("/gift/diy/price")
@MyMenuCheck(auth = "gift:diy:price",desc = "用户礼品DIY价格策略管理")
public class DiyPriceController extends BaseController {
    @Autowired
    private DiyPriceService diyPriceService;
    public DiyPriceController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }

    //获取单个礼品DIY价格策略
    @GetMapping("/getById/{_id}")
    @MyAuthCheck(auth = "getDiyPriceById",desc = "根据DIY价格ID获取DIY价格策略信息")
    public ResultMsg<JSONObject> getById(@PathVariable String _id){
        return ResultMsg.success(diyPriceService.getById(_id));
    }
    //新增/更新礼品DIY价格策略
    @PostMapping("/save")
    @MyAuthCheck(auth = "saveDiyPrice",desc = "新增/更新礼品DIY价格策略")
    public ResultMsg<JSONObject> saveDiyPrice(@RequestBody JSONObject DiyPrice){
        return ResultMsg.success(diyPriceService.saveOrUpdate(DiyPrice));
    }
    //删除礼品DIY价格策略
    @DeleteMapping("/delete/{_id}")
    @MyAuthCheck(auth = "deleteDiyPrice",desc = "删除DIY价格策略")
    public ResultMsg<Boolean> deleteDiyPrice(@PathVariable String _id){
        return ResultMsg.success(diyPriceService.delete(_id));
    }
    //批量删除礼品DIY价格策略
    @DeleteMapping("/deleteBatchIds")
    @MyAuthCheck(auth = "deleteBatchDiyPrice",desc = "批量删除DIY价格策略")
    public ResultMsg<Boolean> deleteBatchDiyPrice(@RequestBody JSONObject ids){
        List<String> idList  = ids.getJSONArray("ids").toJavaList(String.class);
        return ResultMsg.success(diyPriceService.deleteBatchIds(idList));
    }
    //分页查询礼品DIY价格策略
    @PostMapping("/page")
    @MyAuthCheck(auth = "pageDiyPrice",desc = "分页查询礼品DIY价格策略")
    public ResultMsg<JSONObject> page(@RequestBody JSONObject page){
        return ResultMsg.success(diyPriceService.page(page));
    }
    //分页条件查询礼品DIY价格策略
    @PostMapping("/pageCondition")
    @MyAuthCheck(auth = "pageConditionDiyPrice",desc = "分页条件查询礼品DIY价格策略")
    public ResultMsg<JSONObject> pageCondition(@RequestBody JSONObject page){
        return ResultMsg.success(diyPriceService.pageCondition(page));
    }
    //获取全部DIY价格策略
    @GetMapping("/getAllData")
    @MyAuthCheck(auth = "getAllDataDiyPrice",desc = "获取全部DIY价格策略")
    public ResultMsg<JSONObject> getAllData(){
        return ResultMsg.success(diyPriceService.getAllData());
    }

}
