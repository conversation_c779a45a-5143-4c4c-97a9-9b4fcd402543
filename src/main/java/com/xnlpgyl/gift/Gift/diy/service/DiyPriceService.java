package com.xnlpgyl.gift.Gift.diy.service;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.MongoDbColName;
import com.xnlpgyl.gift.Gift.config.MongoPage;
import com.xnlpgyl.gift.Gift.diy.dao.DiyPriceDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Pattern;

@Service
public class DiyPriceService {

    @Autowired
    private DiyPriceDao diyPriceDao;
    public JSONObject getById(String _id) {
        return diyPriceDao.getById(_id, MongoDbColName.GIFT_DIY_PRICE);
    }

    public JSONObject saveOrUpdate(JSONObject giftGoods) {
        giftGoods.put("updateTime", System.currentTimeMillis());
        if (giftGoods.get("_id") == null){
            giftGoods.put("createTime", System.currentTimeMillis());
            giftGoods.put("_id", UUID.fastUUID().toString());
        }
        return diyPriceDao.saveOrUpdate(giftGoods,MongoDbColName.GIFT_DIY_PRICE);
    }

    public boolean delete(String _id) {
        return diyPriceDao.deleteById(_id,MongoDbColName.GIFT_DIY_PRICE);
    }

    public JSONObject page(JSONObject page) {
        Query query = new Query();
        JSONObject result = new JSONObject();
        result.put("total", diyPriceDao.count(query,MongoDbColName.GIFT_DIY_PRICE));
        int pageNum = page.getIntValue("pageNum");
        int pageSize = page.getIntValue("pageSize");
        MongoPage.getPageQuery(query,  pageNum, pageSize);
        result.put("data", diyPriceDao.page(query,MongoDbColName.GIFT_DIY_PRICE));
        return result;
    }

    public JSONObject pageCondition(JSONObject page) {
        Query query = new Query();
        JSONObject conditionJson = page.getJSONObject("condition");
        if (conditionJson != null) {
            Criteria condition = new Criteria();
            conditionJson.forEach((k, v) -> {
                if (v == null || v.equals("")) {
                    return;
                }
                if (v instanceof String) {
                    v = v.toString().trim();
                }
                //模糊查询 不区分大小写
                Pattern pattern = Pattern.compile("^.*" + v + ".*$", Pattern.CASE_INSENSITIVE);
                condition.and(k).regex(pattern);
            });
            query.addCriteria(condition);
        }
        long count = diyPriceDao.count(query, MongoDbColName.GIFT_DIY_PRICE);
        int pageNum = page.getIntValue("pageNum");
        int pageSize = page.getIntValue("pageSize");
        MongoPage.getPageQuery(query, pageNum, pageSize);
        if (page.getJSONObject("sort") != null) {
            JSONObject sortJson = page.getJSONObject("sort");
            sortJson.forEach((k, v) -> {
                if (v.equals("asc")) {
                    query.with(Sort.by(Sort.Direction.ASC, k));
                } else {
                    query.with(Sort.by(Sort.Direction.DESC, k));
                }
            });
        }
        JSONObject result = new JSONObject();
        result.put("total", count);
        result.put("data", diyPriceDao.page(query, MongoDbColName.GIFT_DIY_PRICE));
        return result;
    }

    public boolean deleteBatchIds(List<String> idList) {
        if (idList != null && !idList.isEmpty()) {
            idList.forEach(id -> diyPriceDao.deleteById(id, MongoDbColName.GIFT_DIY_PRICE));
        }
        return true;
    }

    public JSONObject getAllData() {
        return new JSONObject().fluentPut("data", diyPriceDao.getAllList(MongoDbColName.GIFT_DIY_PRICE));
    }

}
