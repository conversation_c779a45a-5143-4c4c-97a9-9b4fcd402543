package com.xnlpgyl.gift.Gift.giftGoods.dao;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.BaseDao;
import com.xnlpgyl.gift.Gift.config.MongoDbColName;
import jakarta.validation.constraints.Min;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

@Repository
public class GiftGoodsTypeDao extends BaseDao {
}
