package com.xnlpgyl.gift.Gift.giftGoods.controller;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.config.BusinessException;
import com.xnlpgyl.gift.Gift.giftGoods.service.GiftGoodsTypeService;
import com.xnlpgyl.gift.Gift.security.MyAuthCheck;
import com.xnlpgyl.gift.Gift.security.MyMenuCheck;
import com.xnlpgyl.gift.Gift.utils.ResultCode;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.Min;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/gift/giftGoodsType")
@MyMenuCheck(auth = "gift:giftGoodsType:",desc = "礼品分类管理")
public class GiftGoodsTypeController  extends BaseController {
    @Autowired
    private GiftGoodsTypeService giftGoodsTypeService;
    public GiftGoodsTypeController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }


    /**
     * 根据ID获取商品
     *
     * @param _id 商品ID
     * @return 商品信息
     */
    @GetMapping("/getById/{_id}")
    @MyAuthCheck(auth = "getGiftGoodsTypeById",desc = "根据礼品分类ID获取礼品分类信息")
    public ResultMsg<JSONObject> getGiftGoodsTypeById(@PathVariable String _id) {
        JSONObject giftGoods = giftGoodsTypeService.getById(_id);
        if (giftGoods == null) {
            throw new BusinessException(ResultCode.GOODS_NOT_EXIST);
        }
        return ResultMsg.success(giftGoods);
    }

    /**
     * 保存/更新礼品分类信息
     * @param giftGoodsType 礼品分了信息
     * @return 商品分类信息
     */
    @PostMapping("/save")
    @MyAuthCheck(auth = "saveGiftGoodsType",desc = "新增/更新礼品分类信息")
    public ResultMsg<JSONObject> saveGiftGoodsType(@RequestBody JSONObject giftGoodsType) {
        JSONObject suc = giftGoodsTypeService.saveOrUpdate(giftGoodsType);
        if (suc==null) {
            throw new BusinessException(ResultCode.ERROR);
        }
        return ResultMsg.success("保存成功");
    }

    /**
     * 删除礼品分类信息
     * @param _id 商品ID
     * @return 是否删除成功
     */
    @DeleteMapping("/delete/{_id}")
    @MyAuthCheck(auth = "deleteGiftGoodsType",desc = "删除礼品分类信息")
    public ResultMsg<JSONObject> deleteGiftGoodsType(@PathVariable String _id) {
        boolean suc = giftGoodsTypeService.delete(_id);
        if (!suc) {
            throw new BusinessException(ResultCode.ERROR);
        }
        return ResultMsg.success("删除成功");
    }
    /**
     * 批量删除礼品分类信息
     * @param ids 礼品分类IDs
     * @return 是否删除成功
     */
    @DeleteMapping("/deleteBatchesGiftGoodsTypes")
    @MyAuthCheck(auth = "deleteBatchesGiftGoodsTypes",desc = "批量删除礼品分类信息")
    public ResultMsg<JSONObject> deleteBatchesGiftGoodsTypes(@RequestBody JSONObject ids)  {
        List<String> idList = ids.getJSONArray("ids").toJavaList(String.class);
        boolean suc = giftGoodsTypeService.deleteBatchIds(idList);
        if (!suc) {
            throw new BusinessException(ResultCode.ERROR);
        }
        return ResultMsg.success("删除成功");
    }
    /**
     * 分页查询礼品分类信息
     */
    @PostMapping("/page")
    @MyAuthCheck(auth = "pageGiftGoodsType",desc = "分页查询礼品分类信息")
    public ResultMsg<JSONObject> pageGiftGoodsType(@RequestBody JSONObject page) {
        JSONObject result = giftGoodsTypeService.page(page);
        return ResultMsg.success(result);
    }
    /**
     * 分页条件查询礼品信息
     */
    @PostMapping("/pageCondition")
    @MyAuthCheck(auth = "pageConditionGiftGoodsType",desc = "分页条件查询礼品分类信息")
    public ResultMsg<JSONObject> pageConditionGiftGoodsType(@RequestBody JSONObject page) {
        JSONObject result = giftGoodsTypeService.pageCondition(page);
        return ResultMsg.success(result);
    }

    /**
     * 获取全部数据
     */
    @GetMapping("/getAllData")
    @MyAuthCheck(auth = "getAllData",desc = "获取全部礼品分类数据")
    public ResultMsg<JSONObject> getAllData() {
        JSONObject result = giftGoodsTypeService.getAllData();
        return ResultMsg.success(result);
    }

}
