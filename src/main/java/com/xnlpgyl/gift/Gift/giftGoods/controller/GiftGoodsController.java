package com.xnlpgyl.gift.Gift.giftGoods.controller;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.config.BusinessException;
import com.xnlpgyl.gift.Gift.giftGoods.service.GiftGoodsService;
import com.xnlpgyl.gift.Gift.security.MyAuthCheck;
import com.xnlpgyl.gift.Gift.security.MyMenuCheck;
import com.xnlpgyl.gift.Gift.utils.ResultCode;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.Min;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/gift/giftGoods")
@MyMenuCheck(auth = "gift:giftGoods:",desc = "礼品管理")
public class GiftGoodsController extends BaseController {
    @Autowired
    private GiftGoodsService giftGoodsService;
    public GiftGoodsController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }

    /**
     * 根据ID获取商品
     *
     * @param _id 商品ID
     * @return 商品信息
     */
    @GetMapping("/getById/{_id}")
    @MyAuthCheck(auth = "getGiftGoodsById",desc = "根据礼品ID获取礼品信息")
    public ResultMsg<JSONObject> getById(@PathVariable  String _id) {
        JSONObject giftGoods = giftGoodsService.getById(_id);
        if (giftGoods == null) {
            throw new BusinessException(ResultCode.GOODS_NOT_EXIST);
        }
        return ResultMsg.success(giftGoods);
    }

    /**
     * 保存/更新礼品信息
     * @param giftGoods 商品信息
     * @return 商品信息
     */
    @PostMapping("/save")
    @MyAuthCheck(auth = "saveGiftGoods",desc = "新增/更新礼品信息")
    public ResultMsg<JSONObject> saveGiftGoods(@RequestBody JSONObject giftGoods) {
        JSONObject suc = giftGoodsService.saveOrUpdate(giftGoods);
        if (suc==null) {
            throw new BusinessException(ResultCode.ERROR);
        }
        return ResultMsg.success("保存成功");
    }

    /**
     * 删除礼品信息
     * @param _id 商品ID
     * @return 是否删除成功
     */
    @DeleteMapping("/delete/{_id}")
    @MyAuthCheck(auth = "deleteGiftGoods",desc = "删除礼品信息")
    public ResultMsg<JSONObject> deleteGiftGoods(@PathVariable String _id) {
        boolean suc = giftGoodsService.delete(_id);
        if (!suc) {
            throw new BusinessException(ResultCode.ERROR);
        }
        return ResultMsg.success("删除成功");
    }
    /**
     * 批量删除礼品信息
     * @param ids 商品IDs
     * @return 是否删除成功
     */
    @DeleteMapping("/deleteBatchesGiftGoods")
    @MyAuthCheck(auth = "deleteBatchesGiftGoods",desc = "批量删除礼品信息")
    public ResultMsg<JSONObject> deleteBatchesGiftGoods(@RequestBody JSONObject ids)  {
       List<String> idList = ids.getJSONArray("ids").toJavaList(String.class);
       boolean suc = giftGoodsService.deleteBatchIds(idList);
       if (!suc) {
           throw new BusinessException(ResultCode.ERROR);
       }
       return ResultMsg.success("删除成功");
    }
    /**
     * 分页查询礼品信息
     */
    @PostMapping("/page")
    @MyAuthCheck(auth = "pageGiftGoods",desc = "分页查询礼品信息")
    public ResultMsg<JSONObject> pageGiftGoods(@RequestBody JSONObject page) {
        JSONObject result = giftGoodsService.page(page);
        return ResultMsg.success(result);
    }
    /**
     * 分页条件查询礼品信息
     */
    @PostMapping("/pageCondition")
    @MyAuthCheck(auth = "pageConditionGiftGoods",desc = "分页条件查询礼品信息")
    public ResultMsg<JSONObject> pageConditionGiftGoods(@RequestBody JSONObject page) {
        JSONObject result = giftGoodsService.pageCondition(page);
        return ResultMsg.success(result);
    }


}
