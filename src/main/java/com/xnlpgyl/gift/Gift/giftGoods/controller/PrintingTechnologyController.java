package com.xnlpgyl.gift.Gift.giftGoods.controller;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.giftGoods.service.PrintingTechnologyService;
import com.xnlpgyl.gift.Gift.security.MyAuthCheck;
import com.xnlpgyl.gift.Gift.security.MyMenuCheck;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/gift/printingTechnology")
@MyMenuCheck(auth = "gift:printingTechnology:",desc = "礼品印刷工艺管理")
public class PrintingTechnologyController   extends BaseController {

    @Autowired
    private PrintingTechnologyService printingTechnologyService;
    public PrintingTechnologyController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }
    //获取单个礼品印刷工艺信息
    @GetMapping("/getById/{_id}")
    @MyAuthCheck(auth = "getPrintingTechnologyById",desc = "根据礼品印刷工艺ID获取礼品印刷工艺信息")
    public ResultMsg<JSONObject> getById(@PathVariable String _id){
        return ResultMsg.success(printingTechnologyService.getById(_id));
    }
    //新增/更新礼品印刷工艺信息
    @PostMapping("/save")
    @MyAuthCheck(auth = "savePrintingTechnology",desc = "新增/更新礼品印刷工艺信息")
    public ResultMsg<JSONObject> savePrintingTechnology(@RequestBody JSONObject printingTechnology){
        return ResultMsg.success(printingTechnologyService.saveOrUpdate(printingTechnology));
    }
    //删除礼品印刷工艺信息
    @DeleteMapping("/delete/{_id}")
    @MyAuthCheck(auth = "deletePrintingTechnology",desc = "删除礼品印刷工艺信息")
    public ResultMsg<Boolean> deletePrintingTechnology(@PathVariable String _id){
        return ResultMsg.success(printingTechnologyService.delete(_id));
    }
    //批量删除礼品印刷工艺信息
    @DeleteMapping("/deleteBatchIds")
    @MyAuthCheck(auth = "deleteBatchPrintingTechnology",desc = "批量删除礼品印刷工艺信息")
    public ResultMsg<Boolean> deleteBatchPrintingTechnology(@RequestBody JSONObject ids){
        List<String> idList  = ids.getJSONArray("ids").toJavaList(String.class);
        return ResultMsg.success(printingTechnologyService.deleteBatchIds(idList));
    }
    //分页查询礼品印刷工艺信息
    @PostMapping("/page")
    @MyAuthCheck(auth = "pagePrintingTechnology",desc = "分页查询礼品印刷工艺信息")
    public ResultMsg<JSONObject> page(@RequestBody JSONObject page){
        return ResultMsg.success(printingTechnologyService.page(page));
    }
    //分页条件查询礼品印刷工艺信息
    @PostMapping("/pageCondition")
    @MyAuthCheck(auth = "pageConditionPrintingTechnology",desc = "分页条件查询礼品印刷工艺信息")
    public ResultMsg<JSONObject> pageCondition(@RequestBody JSONObject page){
        return ResultMsg.success(printingTechnologyService.pageCondition(page));
    }
    //获取全部礼品印刷工艺数据
    @GetMapping("/getAllData")
    @MyAuthCheck(auth = "getAllDataPrintingTechnology",desc = "获取全部礼品印刷工艺数据")
    public ResultMsg<JSONObject> getAllData(){
        return ResultMsg.success(printingTechnologyService.getAllData());
    }

}
