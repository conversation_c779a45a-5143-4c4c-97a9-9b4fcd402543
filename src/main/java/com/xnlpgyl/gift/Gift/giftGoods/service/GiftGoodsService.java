package com.xnlpgyl.gift.Gift.giftGoods.service;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.MongoDbColName;
import com.xnlpgyl.gift.Gift.config.MongoPage;
import com.xnlpgyl.gift.Gift.giftGoods.dao.GiftGoodsDao;
import com.xnlpgyl.gift.Gift.giftGoods.dao.GiftGoodsTypeDao;
import com.xnlpgyl.gift.Gift.utils.ImageSrcReplacer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Pattern;

@Service("giftGoodsService")
public class GiftGoodsService {
    @Autowired
    private GiftGoodsDao giftGoodsDao;
    @Autowired
    private GiftGoodsTypeDao giftGoodsTypeDao;
    @Autowired
    private ImageSrcReplacer imageSrcReplacer;
    public JSONObject getById(String _id) {
        return giftGoodsDao.getById(_id, MongoDbColName.GIFT_GOODS);
    }

    public JSONObject saveOrUpdate(JSONObject giftGoods) {
        giftGoods.put("updateTime", System.currentTimeMillis());
        if (giftGoods.get("_id") == null){
            giftGoods.put("createTime", System.currentTimeMillis());
            giftGoods.put("_id", UUID.fastUUID().toString());
        }
        //讲base64编码的图片转成二进制图片存放在指定目录，并用图片URL路径替换base64编码的图片
        String productDetailsHtml = giftGoods.getString("productDetailsHtml");
        if(productDetailsHtml!=null&&productDetailsHtml.contains("<img src=\"data:image/")){
            giftGoods.put("productDetailsHtml",imageSrcReplacer.replaceImageSrc(productDetailsHtml));
        }
        return giftGoodsDao.saveOrUpdate(giftGoods,MongoDbColName.GIFT_GOODS);
    }

    public boolean delete(String _id) {
        return giftGoodsDao.deleteById(_id,MongoDbColName.GIFT_GOODS);
    }

    public JSONObject page(JSONObject page) {
        Query query = new Query();
        JSONObject result = new JSONObject();
        result.put("total", giftGoodsDao.count(query,MongoDbColName.GIFT_GOODS));
        int pageNum = page.getIntValue("pageNum");
        int pageSize = page.getIntValue("pageSize");
        MongoPage.getPageQuery(query,  pageNum, pageSize);
        result.put("data", giftGoodsDao.page(query,MongoDbColName.GIFT_GOODS));
        return result;
    }

    public JSONObject pageCondition(JSONObject page) {
        Query query = new Query();
        JSONObject conditionJson = page.getJSONObject("condition");
        if (conditionJson != null) {
            Criteria condition = new Criteria();
            conditionJson.forEach((k, v) -> {
                if (v == null || v.equals("")) {
                    return;
                }
                if (v instanceof String) {
                    v = v.toString().trim();
                }
                //模糊查询 不区分大小写
                Pattern pattern = Pattern.compile("^.*" + v + ".*$", Pattern.CASE_INSENSITIVE);
                condition.and(k).regex(pattern);
            });
            query.addCriteria(condition);
        }
        long count = giftGoodsDao.count(query, MongoDbColName.GIFT_GOODS);
        int pageNum = page.getIntValue("pageNum");
        int pageSize = page.getIntValue("pageSize");
        MongoPage.getPageQuery(query, pageNum, pageSize);
        if (page.getJSONObject("sort") != null) {
            JSONObject sortJson = page.getJSONObject("sort");
            sortJson.forEach((k, v) -> {
                if (v.equals("asc")) {
                    query.with(Sort.by(Sort.Direction.ASC, k));
                } else {
                    query.with(Sort.by(Sort.Direction.DESC, k));
                }
            });
        }
        JSONObject result = new JSONObject();
        result.put("total", count);
        List<JSONObject> data = giftGoodsDao.page(query, MongoDbColName.GIFT_GOODS);
        if (data != null && !data.isEmpty()){
            data.forEach(item -> {
                item.remove("_class");
                String giftType = item.getString("giftType");
                if (giftType != null && !giftType.equals("")) {
                    JSONObject giftTypeJson = giftGoodsTypeDao.getById(giftType,MongoDbColName.GIFT_GOODS_TYPE);
                    item.put("giftTypeName", giftTypeJson==null?"未分类":giftTypeJson.getString("giftTypeName"));
                }
            });
        }
        result.put("data", data);
        return result;
    }

    public boolean deleteBatchIds(List<String> idList) {
        if (idList != null && !idList.isEmpty()) {
            idList.forEach(id -> giftGoodsDao.deleteById(id, MongoDbColName.GIFT_GOODS));
        }
        return true;
    }
}
