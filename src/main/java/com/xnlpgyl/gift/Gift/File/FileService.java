package com.xnlpgyl.gift.Gift.File;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.BusinessException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import com.xnlpgyl.gift.Gift.utils.ResultCode;

@Service
public class FileService {
    @Value("${file.upload.path}")
    private String baseFilePath;
    @Getter
    @Value("${file.upload.url}")
    private String uploadUrl;
    @Getter
    @Value("${file.download.url}")
    private String downloadUrl;

    @Autowired
    private FileValidator fileValidator;
    private static void setDownloadResponseHeaders(HttpServletResponse response, File file) {
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + file.getName());
        response.setHeader("Content-Length", String.valueOf(file.length()));
        response.setHeader("Content-Type", "application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename="+ file.getName());
        response.setCharacterEncoding("UTF-8");
    }
    // 使用UUID生成文件名
    private synchronized String generateUniqueFileName(String originalFilename) {
        String suffix = null;
        if (originalFilename != null) {
            suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        //生成新的唯一文件名
        return  UUID.randomUUID()+"_" + System.currentTimeMillis()+"_"+ RandomUtil.randomNumbers(6) + suffix;
    }
    public String saveFile(MultipartFile file) {
        //保存文件到指定路径
        try {
            fileValidator.validateFile(file);
            //获取文件后缀
            String originalFilename = file.getOriginalFilename();
            //生成新的唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            //更具日期建立文件夹
            String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String newFilePath = baseFilePath + date +File.separator;
            File fileDir = new File(newFilePath);
            if (!fileDir.exists()) {
                if(!fileDir.mkdirs()){
                    throw new BusinessException(ResultCode.FILE_SAVE_ERROR);
                }
            }
            //创建文件
            file.transferTo( new File(newFilePath + fileName));
            return date + File.separator + fileName;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public JSONArray saveFiles(MultipartFile[] file) {
        JSONArray jsonArray = new JSONArray();
        for (MultipartFile multipartFile : file) {
            String filePath = saveFile(multipartFile);
            if (filePath != null) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("filePath", filePath);
                jsonArray.add(jsonObject);
            }
        }
        return jsonArray;
    }

    public void downloadFile(String filePath, HttpServletResponse response) {
        try {
            File file = new File(baseFilePath + filePath);
            if (file.exists()) {
                setDownloadResponseHeaders(response, file);
                try (FileInputStream inputStream = new FileInputStream(file); OutputStream outputStream = response.getOutputStream()) {
                    //1页 4KB
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    outputStream.flush();
                }catch (IOException e){
                    e.printStackTrace();
                }
            }else {
                throw new BusinessException(ResultCode.FILE_NOT_FOUND);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }



    public boolean deleteFile(String filePath) {
        try {
            File file = new File(baseFilePath + filePath);
            if (file.exists()) {
                Files.delete(file.toPath());
                return true;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return false;
    }
}
