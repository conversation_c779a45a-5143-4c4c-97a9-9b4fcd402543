package com.xnlpgyl.gift.Gift.File;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.config.BusinessException;
import com.xnlpgyl.gift.Gift.security.MyMenuCheck;
import com.xnlpgyl.gift.Gift.utils.ResultCode;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Base64;

@RestController
@RequestMapping("/gift/file")
@MyMenuCheck(auth = "gift:file:",desc = "文件管理")
public class FileController extends BaseController {
    @Autowired
    private FileService fileService;

    public FileController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }
    //获取上传文件URL地址
    @GetMapping("/getUploadAndDownloadUrl")
    @MyMenuCheck(auth = "getUploadUrl",desc = "获取上传文件URL地址")
    public ResultMsg<JSONObject> getUploadUrl(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("uploadUrl", fileService.getUploadUrl());
        jsonObject.put("downloadUrl", fileService.getDownloadUrl());
        return ResultMsg.success(jsonObject);
    }
    @PostMapping("/upload")
    @MyMenuCheck(auth = "uploadFile",desc = "上传文件")
    public ResultMsg<JSONObject> uploadFile(@RequestParam("file") MultipartFile file){
        return ResultMsg.success(fileService.saveFile(file));
    }
    @PostMapping("/uploads")
    @MyMenuCheck(auth = "uploadFiles",desc = "上传多个文件")
    public ResultMsg<JSONArray> uploadFiles(@RequestParam("files") MultipartFile[] file){
        return ResultMsg.success(fileService.saveFiles(file));
    }
    @GetMapping("/download/{filePathBase64}")
    @MyMenuCheck(auth = "downloadFile",desc = "下载文件")
    public void downloadFile(@PathVariable String filePathBase64,HttpServletResponse response){
        String filePath = new String(Base64.getDecoder().decode(filePathBase64));
        fileService.downloadFile(filePath,response);
    }
    //删除文件
    @DeleteMapping("/deleteFile/{filePathBase64}")
    @MyMenuCheck(auth = "deleteFile",desc = "删除文件")
    public ResultMsg<JSONObject> deleteFile(@PathVariable String filePathBase64){
        String filePath = new String(Base64.getDecoder().decode(filePathBase64));
        boolean suc = fileService.deleteFile(filePath);
        if (!suc) {
            throw new BusinessException(ResultCode.ERROR);
        }
        return ResultMsg.success("删除成功");
    }
}
