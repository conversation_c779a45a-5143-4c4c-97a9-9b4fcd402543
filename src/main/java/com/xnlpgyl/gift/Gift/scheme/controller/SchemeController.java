package com.xnlpgyl.gift.Gift.scheme.controller;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.car.service.ShoppingCartService;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.scheme.service.SchemeService;
import com.xnlpgyl.gift.Gift.security.MyAuthCheck;
import com.xnlpgyl.gift.Gift.security.MyMenuCheck;
import com.xnlpgyl.gift.Gift.utils.ResultCode;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/gift/scheme")
@MyMenuCheck(auth = "gift:scheme:",desc = "礼品方案管理")
public class SchemeController extends BaseController {
    @Autowired
    private ShoppingCartService shoppingCartService;
    @Autowired
    private SchemeService schemeService;
    public SchemeController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }

    @GetMapping("/getById/{_id}")
    @MyAuthCheck(auth = "getSchemeById",desc = "根据方案ID获取具体方案信息")
    public ResultMsg<JSONObject> getById(@PathVariable String _id) {
        return ResultMsg.success(schemeService.getById(_id));
    }
    @PostMapping("/save")
    @MyAuthCheck(auth = "saveScheme",desc = "新增/更新礼品方案信息")
    public ResultMsg<JSONObject> saveScheme(@RequestBody JSONObject scheme) {
        //保存
        JSONObject result = schemeService.saveOrUpdate(scheme);
        //清空购物车
        shoppingCartService.clearCart(scheme.getLongValue("userId"));
        return ResultMsg.success(result);
    }
    @DeleteMapping("/delete/{_id}")
    @MyAuthCheck(auth = "deleteScheme",desc = "删除礼品方案信息")
    public ResultMsg<Boolean> deleteScheme(@PathVariable String _id) {
        return ResultMsg.success(schemeService.delete(_id));
    }
    @DeleteMapping("/deleteBatchesScheme")
    @MyAuthCheck(auth = "deleteBatchesScheme",desc = "批量删除礼品方案信息")
    public ResultMsg<String> deleteBatchesScheme(@RequestBody List<String> idList) {
        return ResultMsg.success(schemeService.deleteBatchIds(idList));
    }
    @PostMapping("/page")
    @MyAuthCheck(auth = "pageScheme",desc = "分页查询礼品方案信息")
    public ResultMsg<JSONObject> page(@RequestBody JSONObject page) {
        return ResultMsg.success(schemeService.page(page));
    }
    //查询我的礼品方案
    @PostMapping("/pageMyScheme")
    @MyAuthCheck(auth = "pageMyScheme",desc = "分页查询我的礼品方案信息")
    public ResultMsg<JSONObject> pageMyScheme(@RequestBody JSONObject page) {
        return ResultMsg.success(schemeService.pageCondition(page));
    }
    //修改方案的状态
    @PostMapping("/updateStatus")
    @MyAuthCheck(auth = "updateStatus",desc = "修改方案的状态")
    public ResultMsg<JSONObject> updateStatus(@RequestBody JSONObject scheme) {
        //检查参数
        if (scheme == null || scheme.getString("id") == null || scheme.getString("status") == null) {
            return ResultMsg.error(ResultCode.PARAM_ERROR);
        }
        return ResultMsg.success(schemeService.updateStatus(scheme));
    }
    //通过userId查询方案
    @GetMapping("/getByUserId/{userId}")
    @MyAuthCheck(auth = "getByUserId",desc = "通过userId查询方案")
    public ResultMsg<List<JSONObject>> getByUserId(@PathVariable long userId) {
        return ResultMsg.success(schemeService.getByUserId(userId));
    }

    /**
     * 根据用户名分页查询方案列表
     * @param page 分页参数，需包含username字段
     * @return 分页结果
     */
    @PostMapping("/pageByUserId")
    @MyAuthCheck(auth = "pageByUserId", desc = "根据用户ID分页查询方案信息")
    public ResultMsg<JSONObject> pageByUserId(@RequestBody JSONObject page) {
        // 验证用户ID参数
        if (page == null || page.getLong("userId") == null) {
            return ResultMsg.error(ResultCode.PARAM_ERROR);
        }
        //分页参数判断
        if (page.getInteger("pageNum") == null || page.getInteger("pageSize") == null) {
            return ResultMsg.error(ResultCode.PARAM_ERROR);
        }
        // 分页查询方案列表
        return ResultMsg.success(schemeService.pageByUserId(page));
    }
}
