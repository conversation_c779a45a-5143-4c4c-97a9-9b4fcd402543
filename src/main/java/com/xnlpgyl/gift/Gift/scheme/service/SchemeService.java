package com.xnlpgyl.gift.Gift.scheme.service;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.config.MongoDbColName;
import com.xnlpgyl.gift.Gift.config.MongoPage;
import com.xnlpgyl.gift.Gift.scheme.dao.SchemeDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Service
public class SchemeService {

    @Autowired
    private SchemeDao schemeDao;

    public JSONObject getById(String id) {
        return schemeDao.getById(id,MongoDbColName.GIFT_SCHEME);
    }

    public JSONObject saveOrUpdate(JSONObject scheme) {
        if (!scheme.containsKey("_id") || scheme.getString("_id") == null || scheme.getString("_id").trim().isEmpty()) {
            //新增
            scheme.put("_id", UUID.fastUUID().toString());
            scheme.put("createTime", System.currentTimeMillis());
            scheme.put("updateTime", System.currentTimeMillis());
        }else {
            //修改
            scheme.put("updateTime", System.currentTimeMillis());
        }
        return schemeDao.saveOrUpdate(scheme,MongoDbColName.GIFT_SCHEME);
    }

    public boolean delete(String id) {
        return schemeDao.deleteById(id, MongoDbColName.GIFT_SCHEME);
    }

    public String deleteBatchIds(List<String> idList) {
        if (idList != null && !idList.isEmpty()) {
            idList.forEach(id -> schemeDao.deleteById(id, MongoDbColName.GIFT_SCHEME));
        }
        return "删除成功";
    }

    public JSONObject page(JSONObject page) {
        Query query = new Query();
        JSONObject result = new JSONObject();
        result.put("total", schemeDao.count(query,MongoDbColName.GIFT_SCHEME));
        int pageNum = page.getIntValue("pageNum");
        int pageSize = page.getIntValue("pageSize");
        MongoPage.getPageQuery(query,  pageNum, pageSize);
        result.put("data", schemeDao.page(query,MongoDbColName.GIFT_SCHEME));
        return result;
    }
    public JSONObject pageCondition(JSONObject page) {
        Query query = new Query();
        JSONObject conditionJson = page.getJSONObject("condition");
        if (conditionJson != null) {
            Criteria condition = new Criteria();
            conditionJson.forEach((k, v) -> {
                if (v == null || v.equals("")) {
                    return;
                }
                if (v instanceof String) {
                    v = v.toString().trim();
                }
                //模糊查询 不区分大小写
                Pattern pattern = Pattern.compile("^.*" + v + ".*$", Pattern.CASE_INSENSITIVE);
                condition.and(k).regex(pattern);
            });
            query.addCriteria(condition);
        }
        long count = schemeDao.count(query, MongoDbColName.GIFT_SCHEME);
        int pageNum = page.getIntValue("pageNum");
        int pageSize = page.getIntValue("pageSize");
        MongoPage.getPageQuery(query, pageNum, pageSize);
        if (page.getJSONObject("sort") != null) {
            JSONObject sortJson = page.getJSONObject("sort");
            sortJson.forEach((k, v) -> {
                if (v.equals("asc")) {
                    query.with(Sort.by(Sort.Direction.ASC, k));
                } else {
                    query.with(Sort.by(Sort.Direction.DESC, k));
                }
            });
        }
        JSONObject result = new JSONObject();
        result.put("total", count);
        result.put("data", schemeDao.page(query, MongoDbColName.GIFT_SCHEME));
        return result;
    }

    public JSONObject updateStatus(JSONObject scheme) {
        return schemeDao.updateStatus(scheme,MongoDbColName.GIFT_SCHEME);

    }

    public List<JSONObject> getByUserId(long userId) {
        Map<String, Object> fields = new HashMap<>();
        fields.put("userId", userId);
        return schemeDao.getByFields(fields, MongoDbColName.GIFT_SCHEME);
    }

    /**
     * 根据用户ID分页查询方案列表
     * @param page 分页参数，需包含userId字段
     * @return 分页结果
     */
    public JSONObject pageByUserId(JSONObject page) {
        if (page == null || page.getLong("userId") == null) {
            throw new IllegalArgumentException("用户ID参数缺失");
        }
        
        // 创建查询对象
        Query query = new Query();
        // 使用精确匹配查询userId
        query.addCriteria(Criteria.where("userId").is(page.getLong("userId")));
        
        // 获取总数
        long count = schemeDao.count(query, MongoDbColName.GIFT_SCHEME);
        
        // 设置分页
        int pageNum = page.getIntValue("pageNum");
        int pageSize = page.getIntValue("pageSize");
        MongoPage.getPageQuery(query, pageNum, pageSize);
        
        // 设置排序
        if (page.getJSONObject("sort") != null) {
            JSONObject sortJson = page.getJSONObject("sort");
            sortJson.forEach((k, v) -> {
                if (v.equals("asc")) {
                    query.with(Sort.by(Sort.Direction.ASC, k));
                } else {
                    query.with(Sort.by(Sort.Direction.DESC, k));
                }
            });
        }
        
        // 执行查询
        JSONObject result = new JSONObject();
        result.put("total", count);
        result.put("data", schemeDao.page(query, MongoDbColName.GIFT_SCHEME));
        return result;
    }
}
