package com.xnlpgyl.gift.Gift.security;

import com.xnlpgyl.gift.Gift.SysAuthorization.entity.SysAuthorization;
import com.xnlpgyl.gift.Gift.User.entity.User;
import lombok.Data;
import lombok.ToString;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Set;

@ToString
@Data
public class LoginUser implements UserDetails {
    private final User user;
    private Collection<SimpleGrantedAuthority> authorities;
    private Set<String> sysAuthorizations;
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        return user.getPassword();
    }

    @Override
    public String getUsername() {
        return user.getUsername();
    }

    @Override
    public boolean isAccountNonExpired() {
        return UserDetails.super.isAccountNonExpired();
    }

    @Override
    public boolean isAccountNonLocked() {
        return !user.getDeleted();
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return UserDetails.super.isCredentialsNonExpired();
    }

    @Override
    public boolean isEnabled() {
        return user.getStatus() == 1;
    }

    public void setAuthoritiesList(Set<String> sysAuthorizations) {
        this.sysAuthorizations = sysAuthorizations;
        authorities = sysAuthorizations.stream().map(SimpleGrantedAuthority::new).toList();
    }
}
