package com.xnlpgyl.gift.Gift.security;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xnlpgyl.gift.Gift.User.entity.User;
import com.xnlpgyl.gift.Gift.User.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
public class LoginService implements UserDetailsService {
    @Autowired
    private IUserService userService;
    @Override
    public LoginUser loadUserByUsername(String username) throws UsernameNotFoundException {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername,username);
        queryWrapper.eq(User::getStatus,1);
        User user = userService.getOne(queryWrapper);
        if(user==null){
            throw new UsernameNotFoundException("用户名不存在");
        }
        return new LoginUser(user);
    }
}
