package com.xnlpgyl.gift.Gift.security;

import com.xnlpgyl.gift.Gift.User.service.IUserService;
import org.springframework.security.crypto.password.PasswordEncoder;

public class SecurityUtils {

    public static LoginUser getLoginUser(String username, IUserService userService) {
        LoginUser loginUser = CheckAuthentication.loginUserMap.get(username);
        if (loginUser == null){
            loginUser = userService.loadLoginUserByUsername(username);
            CheckAuthentication.loginUserMap.put(username, loginUser);
        }
        return loginUser;
    }

    public static String encryptPassword(PasswordEncoder passwordEncoder, String password) {
        return passwordEncoder.encode(password);
    }
}
