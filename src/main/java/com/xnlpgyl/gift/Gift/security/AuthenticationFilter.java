package com.xnlpgyl.gift.Gift.security;

import cn.hutool.log.Log;
import com.alibaba.fastjson2.JSON;
import com.xnlpgyl.gift.Gift.utils.JwtUtil;
import com.xnlpgyl.gift.Gift.utils.ResultCode;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
@Component
public class AuthenticationFilter extends OncePerRequestFilter {
    public static final String[] excludeUri = {"/websocket","/captcha","/User/user/login", "/User/user/register", "/User/user/logout","/about", "/websocket/stats", "/websocket/online-users"};

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private LoginService userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String uri = request.getRequestURI();
        //下载文件不用授权，都可以下载
        if(uri.contains("/gift/file/download/")){
            filterChain.doFilter(request, response);
            return;
        }
        //微信小程序登录相关接口
        if(uri.startsWith("/wx/miniApp/getOpenId/")||uri.startsWith("/wx/miniApp/getTelephoneNumber/")||uri.startsWith("/wx/miniApp/getTelephoneNumberByOpenId/")){
            filterChain.doFilter(request, response);
            return;
        }
        for (String excludeUriItem : excludeUri) {
            if (uri.equals(excludeUriItem)) {
                filterChain.doFilter(request, response);
                return;
            }
        }

        final String authorizationHeader = request.getHeader("Authorization");
        if (authorizationHeader == null){
            responseError(response, ResultMsg.error(ResultCode.UNAUTHORIZED));
            return;
        }

        String username = null;
        String jwt = null;

        if (authorizationHeader.startsWith("Bearer ")) {
            jwt = authorizationHeader.substring(7);
            try {
                username = jwtUtil.extractUsername(jwt);
            } catch (Exception e) {
                responseError(response, ResultMsg.error(e.getMessage()));
                return;
            }
        }
        if (username != null) {
            try{
                LoginUser userDetails = userDetailsService.loadUserByUsername(username);
                if (!jwtUtil.validateToken(jwt, userDetails)) {
                    responseError(response, ResultMsg.error(ResultCode.TOKEN_INVALID));
                    return;
                }
            }catch (UsernameNotFoundException e){
                responseError(response, ResultMsg.error(e.getMessage()));
                return;
            }

        }
        request.setAttribute("username", username);
        filterChain.doFilter(request, response);
    }
    //向HttpServletResponse response 返回错误json对象数据
    private void responseError(HttpServletResponse response, ResultMsg<?> resultMsg) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(401);
        response.setCharacterEncoding("UTF-8");
        resultMsg.setSuccess(false);
        response.getWriter().write(JSON.toJSONString(resultMsg));
        response.getWriter().flush();
        response.getWriter().close();
    }
}
