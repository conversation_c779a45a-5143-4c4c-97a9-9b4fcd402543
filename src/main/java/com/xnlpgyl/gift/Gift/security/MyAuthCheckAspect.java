package com.xnlpgyl.gift.Gift.security;


import com.xnlpgyl.gift.Gift.User.service.IUserService;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.config.BusinessException;
import com.xnlpgyl.gift.Gift.utils.ResultCode;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Set;

@Aspect
@Component
public class MyAuthCheckAspect {
    @Autowired
    private IUserService userService;

    /**
     * 定义切点：所有带有 @MyAuthCheck 注解的方法
     */
    @Pointcut("@annotation(com.xnlpgyl.gift.Gift.security.MyAuthCheck)")
    public void authCheckPointcut() {}

    /**
     * 定义切点：所有 controller 包下的方法
     */
    @Pointcut("within(com.xnlpgyl.gift.Gift..controller.*)")
    public void controllerPointcut() {}

    /**
     * 权限校验：针对带有 @MyAuthCheck 注解的 controller 方法
     */
    @Before("authCheckPointcut() && controllerPointcut()")
    public void checkPermission(JoinPoint joinPoint) {
        MyMenuCheck myMenuCheck = AopUtils.getTargetClass(joinPoint.getThis()).getAnnotation(MyMenuCheck.class);
        if (myMenuCheck == null) {
            return;
        }
        String baseAuth = myMenuCheck.auth();
        String username = getUsername(joinPoint);
        LoginUser loginUser = SecurityUtils.getLoginUser(username, userService);
        if (loginUser == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "用户未登录");
        }
        //超级管理员直接通过
        if(loginUser.getUser().getRoles().stream().anyMatch(role -> role.getRoleKey().equals("administrator") )){
            return;
        }
        // 获取方法上的MyAuthCheck注解
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        MyAuthCheck authCheck = method.getAnnotation(MyAuthCheck.class);
        // 获取注解中定义的权限
        String requiredPermission = authCheck.auth();
        // 如果注解没有指定具体权限，则只要求用户已登录即可
        if (requiredPermission == null || requiredPermission.isEmpty()) {
            return;
        }
        requiredPermission = baseAuth + requiredPermission;
        // 获取用户的权限列表
        Collection<? extends GrantedAuthority> loginUserAuthorities = loginUser.getAuthorities();
        // 检查用户是否具有所需的权限
        String finalRequiredPermission = requiredPermission;
        if (loginUserAuthorities.stream().noneMatch(authority -> authority.getAuthority().equals(finalRequiredPermission))) {
            throw new BusinessException(ResultCode.FORBIDDEN, "权限不足");
        }
    }

    private static String getUsername(JoinPoint joinPoint) {
        BaseController o = (BaseController) joinPoint.getThis();
        HttpServletRequest request =  o.getRequest();
        Object username = request.getAttribute("username");
        if (username == null){
            return "";
        }
        return (String) username;
    }
}