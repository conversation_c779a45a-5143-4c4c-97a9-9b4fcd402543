# API Token登录模式用户管理接口说明

## 🔑 API Token登录模式特点

由于系统使用API Token登录方式，没有传统的用户登录状态管理，因此在用户管理接口中需要特殊处理：

### 与传统登录的区别

| 特性 | 传统登录模式 | API Token模式 |
|------|-------------|---------------|
| **用户状态** | 服务器维护登录状态 | 无服务器端登录状态 |
| **用户识别** | 从Session/JWT中获取 | 前端传递username参数 |
| **权限验证** | 基于登录用户身份 | 基于API Token权限 |
| **接口设计** | 操作"当前用户" | 操作"指定用户" |

## 📋 修改后的接口设计

### 1. 修改昵称接口

**原设计（传统登录）**:
```json
PUT /user/updateNickname
{
  "nickname": "新昵称"
}
```

**新设计（API Token）**:
```json
PUT /user/updateNickname
{
  "username": "要修改的用户名",
  "nickname": "新昵称"
}
```

### 2. 修改密码接口

**原设计（传统登录）**:
```json
PUT /user/updatePassword
{
  "currentPassword": "当前密码",
  "newPassword": "新密码",
  "confirmPassword": "确认密码"
}
```

**新设计（API Token）**:
```json
PUT /user/updatePassword
{
  "username": "要修改的用户名",
  "currentPassword": "当前密码",
  "newPassword": "新密码",
  "confirmPassword": "确认密码"
}
```

### 3. 获取用户信息接口

**原设计（传统登录）**:
```
GET /user/getCurrentUser
```

**新设计（API Token）**:
```
GET /user/getUserInfo?username=要查询的用户名
```

## 🔧 技术实现变化

### DTO类更新

#### UpdateNicknameRequest.java
```java
public class UpdateNicknameRequest {
    @NotBlank(message = "用户名不能为空")
    private String username;  // 新增字段
    
    @NotBlank(message = "昵称不能为空")
    @Size(min = 1, max = 50, message = "昵称长度必须在1-50个字符之间")
    private String nickname;
    
    // 构造函数和getter/setter方法
}
```

#### UpdatePasswordRequest.java
```java
public class UpdatePasswordRequest {
    @NotBlank(message = "用户名不能为空")
    private String username;  // 新增字段
    
    @NotBlank(message = "当前密码不能为空")
    private String currentPassword;
    
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "新密码长度必须在6-20个字符之间")
    private String newPassword;
    
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;
    
    // 构造函数和getter/setter方法
}
```

### 控制器方法更新

```java
// 修改昵称
@PutMapping("/updateNickname")
@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
public ResultMsg<Boolean> updateNickname(@Valid @RequestBody UpdateNicknameRequest request) {
    // 从request.getUsername()获取用户名，而不是从登录状态
    String username = request.getUsername();
    // ... 业务逻辑
}

// 修改密码
@PutMapping("/updatePassword")
@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
public ResultMsg<Boolean> updatePassword(@Valid @RequestBody UpdatePasswordRequest request) {
    // 从request.getUsername()获取用户名，而不是从登录状态
    String username = request.getUsername();
    // ... 业务逻辑
}

// 获取用户信息
@GetMapping("/getUserInfo")
public ResultMsg<User> getUserInfo(@RequestParam("username") String username) {
    // 直接使用传入的username参数
    // ... 业务逻辑
}
```

## 🚀 前端集成示例

### JavaScript示例

```javascript
// 存储当前操作的用户名
const currentUsername = 'testuser';

// 修改昵称
function updateNickname(newNickname) {
    fetch('/user/updateNickname', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + getApiToken()
        },
        body: JSON.stringify({
            username: currentUsername,  // 必须传递username
            nickname: newNickname
        })
    });
}

// 修改密码
function updatePassword(currentPassword, newPassword, confirmPassword) {
    fetch('/user/updatePassword', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + getApiToken()
        },
        body: JSON.stringify({
            username: currentUsername,  // 必须传递username
            currentPassword: currentPassword,
            newPassword: newPassword,
            confirmPassword: confirmPassword
        })
    });
}

// 获取用户信息
function getUserInfo() {
    fetch(`/user/getUserInfo?username=${currentUsername}`, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + getApiToken()
        }
    });
}
```

## ⚠️ 重要注意事项

### 1. 安全考虑
- **用户名验证**: 后端需要验证传入的username是否有效
- **权限控制**: 虽然可以指定任意用户名，但需要确保API Token有相应权限
- **参数验证**: 所有username参数都必须进行非空验证

### 2. 前端实现
- **用户名管理**: 前端需要维护当前操作的用户名
- **参数传递**: 每个请求都必须包含username参数
- **错误处理**: 需要处理用户名不存在的情况

### 3. 后端实现
- **参数验证**: 使用`@NotBlank`验证username参数
- **用户查询**: 通过username查询用户，而不是从登录状态获取
- **错误响应**: 提供清晰的错误信息

## 🧪 测试验证

所有接口都已通过单元测试验证：

```bash
mvn test -Dtest=UserControllerTest
```

测试覆盖：
- ✅ DTO类功能测试（包含username字段）
- ✅ 参数验证测试
- ✅ 边界条件测试
- ✅ 安全性测试
- ✅ 数据完整性测试

## 📊 总结

API Token登录模式的用户管理接口已经完全实现：

- ✅ 支持通过username参数指定操作用户
- ✅ 完整的参数验证和错误处理
- ✅ 事务管理和数据一致性
- ✅ 安全的密码处理
- ✅ 完整的单元测试覆盖
- ✅ 详细的API文档和使用示例

这种设计模式适合API Token认证场景，提供了灵活的用户管理功能。
