package com.xnlpgyl.gift.Gift.User.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xnlpgyl.gift.Gift.SysRole.entity.SysRole;
import com.xnlpgyl.gift.Gift.User.entity.User;
import org.apache.ibatis.annotations.Insert;

import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
public interface UserMapper extends BaseMapper<User> {

    List<SysRole> getRolesByUserId(Long userId);
}
