package com.xnlpgyl.gift.Gift.User.service;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.SysRole.entity.SysRole;
import com.xnlpgyl.gift.Gift.User.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xnlpgyl.gift.Gift.security.LoginUser;

import java.util.List;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
public interface IUserService extends IService<User> {
    public JSONObject userLogin(String username, String password) throws Exception;
    LoginUser loadLoginUserByUsername(String username);
    List<SysRole> getRolesByUserId(Long userId);
    void saveRole(Long userId, List<Long> roleIds);

    List<SysRole> getUserRoles(Long id);
}
