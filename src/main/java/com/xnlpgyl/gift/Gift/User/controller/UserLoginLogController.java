package com.xnlpgyl.gift.Gift.User.controller;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.User.dao.UserLoginLogDao;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.config.MongoPage;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 用户登录日志控制器
 */
@RestController
@RequestMapping("/User/loginLog")
public class UserLoginLogController extends BaseController {

    @Autowired
    private UserLoginLogDao userLoginLogDao;

    public UserLoginLogController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }

    /**
     * 分页查询登录日志
     * @param page 页码
     * @param size 每页大小
     * @param username 用户名（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param success 是否成功（可选）
     * @return 登录日志列表
     */
    @GetMapping("/list")
    public ResultMsg<MongoPage<LinkedHashMap<String, Object>>> getLoginLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @RequestParam(required = false) Boolean success) {

        Query query = new Query();

        // 添加查询条件
        if (StringUtils.isNotBlank(username)) {
            query.addCriteria(Criteria.where("username").is(username));
        }

        if (startTime != null && endTime != null) {
            query.addCriteria(Criteria.where("loginTime").gte(startTime).lte(endTime));
        } else if (startTime != null) {
            query.addCriteria(Criteria.where("loginTime").gte(startTime));
        } else if (endTime != null) {
            query.addCriteria(Criteria.where("loginTime").lte(endTime));
        }

        if (success != null) {
            query.addCriteria(Criteria.where("loginSuccess").is(success));
        }

        // 按登录时间降序排序
        query.with(Sort.by(Sort.Direction.DESC, "loginTime"));

        MongoPage<LinkedHashMap<String, Object>> mongoPage = userLoginLogDao.findPage(query, page, size, UserLoginLogDao.COLLECTION_NAME_LOGIN_LOG);
        return ResultMsg.success(mongoPage);
    }

    /**
     * 获取用户最近的登录日志
     * @param username 用户名
     * @param limit 限制数量
     * @return 登录日志列表
     */
    @GetMapping("/recent")
    public ResultMsg<List<JSONObject>> getRecentLoginLogs(
            @RequestParam String username,
            @RequestParam(defaultValue = "5") Integer limit) {

        Query query = new Query();
        query.addCriteria(Criteria.where("username").is(username));
        query.with(Sort.by(Sort.Direction.DESC, "loginTime"));
        query.limit(limit);

        List<JSONObject> logs = userLoginLogDao.find(query, UserLoginLogDao.COLLECTION_NAME_LOGIN_LOG);
        return ResultMsg.success(logs);
    }

    /**
     * 获取登录统计信息
     * @param days 最近几天（默认7天）
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResultMsg<JSONObject> getLoginStats(@RequestParam(defaultValue = "7") Integer days) {
        // 计算最近days天的时间戳
        long startTime = System.currentTimeMillis() - (days * 24 * 60 * 60 * 1000L);

        // 查询成功登录数量
        Query successQuery = new Query();
        successQuery.addCriteria(Criteria.where("loginTime").gte(startTime));
        successQuery.addCriteria(Criteria.where("loginSuccess").is(true));
        long successCount = userLoginLogDao.count(successQuery, UserLoginLogDao.COLLECTION_NAME_LOGIN_LOG);

        // 查询失败登录数量
        Query failQuery = new Query();
        failQuery.addCriteria(Criteria.where("loginTime").gte(startTime));
        failQuery.addCriteria(Criteria.where("loginSuccess").is(false));
        long failCount = userLoginLogDao.count(failQuery, UserLoginLogDao.COLLECTION_NAME_LOGIN_LOG);

        // 返回统计结果
        JSONObject stats = new JSONObject();
        stats.put("successCount", successCount);
        stats.put("failCount", failCount);
        stats.put("totalCount", successCount + failCount);
        stats.put("days", days);

        return ResultMsg.success(stats);
    }
}
