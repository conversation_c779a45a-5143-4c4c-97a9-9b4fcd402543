package com.xnlpgyl.gift.Gift.User.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xnlpgyl.gift.Gift.SysRole.entity.SysRole;
import com.xnlpgyl.gift.Gift.SysRole.service.ISysRoleService;
import com.xnlpgyl.gift.Gift.User.dao.UserLoginLogDao;
import com.xnlpgyl.gift.Gift.User.entity.User;
import com.xnlpgyl.gift.Gift.User.service.IUserService;
import com.xnlpgyl.gift.Gift.User.service.impl.UserServiceImpl;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.security.SecurityUtils;
import com.xnlpgyl.gift.Gift.utils.JwtUtil;
import com.xnlpgyl.gift.Gift.utils.ResultCode;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import com.xnlpgyl.gift.Gift.wxminiapp.WxMiniAppServerDao;
import com.xnlpgyl.gift.Gift.User.dto.UpdateNicknameRequest;
import com.xnlpgyl.gift.Gift.User.dto.UpdatePasswordRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@RestController
@RequestMapping("/User/user")
public class UserController extends BaseController {

    @Autowired
    private JwtUtil jwtUtil;
    @Autowired
    private WxMiniAppServerDao wxMiniAppServerDao;

    @Autowired
    private IUserService userService;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private UserLoginLogDao userLoginLogDao;

    public UserController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }

    @PostMapping("/login")
    public ResultMsg<JSONObject> login(@RequestBody User user, HttpServletRequest request) throws Exception {
        if (!request.getMethod().equals("POST")) {
            throw new AuthenticationServiceException(
                    "Authentication method not supported: " + request.getMethod());
        }
        
        String username = user.getUsername();
        int status =   user.getStatus()==null?0:user.getStatus();
        //小程序登录 不需要检验验证码
        if (status != 2) {

            // 检查验证码失败次数是否超过限制
            if (UserServiceImpl.isCaptchaFailExceedLimit(username)) {
                // 记录登录失败日志
                saveLoginLog(username, request, false, "验证码失败次数超过限制");
                return ResultMsg.error("验证码失败次数过多，请稍后再试");
            }

            // 验证验证码
            String captcha = user.getCaptcha();
            String sessionCaptcha = (String) request.getSession().getAttribute("captchaAnswer");
            // 清除session中的验证码，确保每次验证只能使用一次
            request.getSession().removeAttribute("captchaAnswer");

            if (sessionCaptcha == null || !sessionCaptcha.equals(captcha)) {
                // 记录验证码失败
                UserServiceImpl.recordCaptchaFail(username);
                int failCount = UserServiceImpl.getCaptchaFailCount(username);

                // 记录登录失败日志
                saveLoginLog(username, request, false, "验证码错误(第" + failCount + "次)");

                if (failCount >= 5) {
                    return ResultMsg.error("验证码失败次数过多，请稍后再试");
                } else {
                    return ResultMsg.error("验证码错误，还可尝试" + (5 - failCount) + "次");
                }
            }
        }

        String password = user.getPassword();
        if (StringUtils.isAnyBlank(username, password)) {
            // 记录登录失败日志
            saveLoginLog(username, request, false, "用户名或密码为空");
            ResultMsg.error(ResultCode.USER_PASSWORD_ERROR);
            throw new AccountExpiredException(ResultCode.USER_PASSWORD_ERROR.getMessage());
        }
        //首先判断账号是否存在
        User userEx = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getUsername, username));
        //小程序登录 如果不存在 则新增用户
        if(userEx ==null && user.getStatus()==2){
            //新增用户 调用新增用户接口
            user.setStatus((byte) 1);
            user.setNickname(username);
            user.setEmail("<EMAIL>");
            user.setPhone(username);
            List<SysRole> roles = new ArrayList<>();
            SysRole role = roleService.getOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleKey, "wxMiniAppUser"));
            assert role != null;
            roles.add(role);
            user.setRoles(roles);
            ResultMsg<Boolean> resultMsg =  addUser(user);
            if(!resultMsg.isSuccess()){
                return ResultMsg.error(resultMsg.getMsg());
            }
            JSONObject userJson = new JSONObject();
            userJson.put("username", username);
            userJson.put("password", password);
            userJson.put("createTime", System.currentTimeMillis());
            wxMiniAppServerDao.saveOrUpdate(userJson,  WxMiniAppServerDao.COLLECTION_NAME_USER);
        }
        try {
            JSONObject result = userService.userLogin(username, password);
            // 记录登录成功日志
            saveLoginLog(username, request, true, null);
            return ResultMsg.success(result);
        } catch (Exception e) {
            // 记录登录失败日志
            saveLoginLog(username, request, false, e.getMessage());
            throw e;
        }
    }

    //角色增删改查
    @PostMapping("/getUsersByPage")
    public ResultMsg<Page<User>> getUsersByPage(@RequestBody Page<User> page) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByDesc(User::getId);
        page = userService.page(page, lambdaQueryWrapper);
        //更新用户的角色信息
        for (User user : page.getRecords()) {
            List<SysRole> roles = userService.getUserRoles(user.getId());
            user.setRoles(roles);
        }
        return ResultMsg.success(page);
    }

    //根据用户id查询用户信息
    @GetMapping("/getUserById/{id}")
    public ResultMsg<User> getUserById(@PathVariable Long id) {
        User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getId, id));
        if (user == null) {
            return ResultMsg.error(ResultCode.USER_NOT_EXIST);
        }
        List<SysRole> roles = userService.getUserRoles(id);
        user.setRoles(roles);
        return ResultMsg.success(user);
    }

    //根据用户名查询用户信息
    @GetMapping("/getUserByUsername/{username}")
    public ResultMsg<User> getUserByUsername(@PathVariable String username) {
        User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getUsername, username));
        if (user == null) {
            return ResultMsg.error(ResultCode.USER_NOT_EXIST);
        }
        List<SysRole> roles = userService.getUserRoles(user.getId());
        user.setRoles(roles);
        return ResultMsg.success(user);
    }

    //模糊查询 username nickname email phone 字段模糊查询用户信息
    @GetMapping("/getUsersByCondition")
    public ResultMsg<Page<User>> getUsersByCondition(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                     @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize,
                                                     @RequestParam(value = "condition", required = false) String condition) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(StringUtils.isNotBlank(condition), User::getUsername, condition)
                .or()
                .like(StringUtils.isNotBlank(condition), User::getNickname, condition)
                .or()
                .like(StringUtils.isNotBlank(condition), User::getEmail, condition)
                .or()
                .like(StringUtils.isNotBlank(condition), User::getPhone, condition);
        Page<User> page = userService.page(new Page<>(pageNum, pageSize), lambdaQueryWrapper);
        //更新用户的角色信息
        for (User user : page.getRecords()) {
            List<SysRole> roles = userService.getUserRoles(user.getId());
            user.setRoles(roles);
        }
        return ResultMsg.success(page);
    }

    /**
     * 新增一个用户
     */
    @PostMapping("/addUser")
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public ResultMsg<Boolean> addUser(@RequestBody User user){
        //密码加密
        if (!user.getPassword().startsWith("$2a$")){
            user.setPassword(SecurityUtils.encryptPassword(passwordEncoder,user.getPassword()));
        }
        userService.saveOrUpdate(user);
        List<SysRole> roleList = user.getRoles();
        if(roleList != null && !roleList.isEmpty()){
            //保存用户角色信息
            List<Long> roleIds = roleList.stream().map(SysRole::getId).toList();
            userService.saveRole(user.getId(),roleIds);
        }
        return ResultMsg.success();
    }
    //删除用户
    @DeleteMapping("/deleteUser/{id}")
    public ResultMsg<Boolean> deleteUser(@PathVariable Long id){
        return ResultMsg.success(userService.removeById(id));
    }
    //删除多个用户
    @DeleteMapping("/deleteUsers")
    public ResultMsg<Boolean> deleteUsers(@RequestBody List<Long> ids){
        return ResultMsg.success(userService.removeByIds(ids));
    }

    /**
     * 修改用户昵称
     *
     * @param request 修改昵称请求
     * @return 修改结果
     */
    @PutMapping("/updateNickname")
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public ResultMsg<Boolean> updateNickname(@Valid @RequestBody UpdateNicknameRequest request) {
        try {
            // 验证username参数
            if (StringUtils.isBlank(request.getUsername())) {
                return ResultMsg.error("用户名不能为空");
            }

            // 查询用户
            User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getUsername, request.getUsername()));
            if (user == null) {
                return ResultMsg.error(ResultCode.USER_NOT_EXIST);
            }

            // 检查昵称是否与当前昵称相同
            if (request.getNickname().equals(user.getNickname())) {
                return ResultMsg.error("新昵称与当前昵称相同");
            }

            // 更新昵称
            user.setNickname(request.getNickname());
            boolean success = userService.updateById(user);

            if (success) {
                return ResultMsg.success("昵称修改成功");
            } else {
                return ResultMsg.error("昵称修改失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            return ResultMsg.error("修改昵称时发生异常：" + e.getMessage());
        }
    }

    /**
     * 修改用户密码
     *
     * @param request 修改密码请求
     * @return 修改结果
     */
    @PutMapping("/updatePassword")
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public ResultMsg<Boolean> updatePassword(@Valid @RequestBody UpdatePasswordRequest request) {
        try {
            // 验证新密码和确认密码是否一致
            if (!request.getNewPassword().equals(request.getConfirmPassword())) {
                return ResultMsg.error("新密码和确认密码不一致");
            }

            // 验证username参数
            if (StringUtils.isBlank(request.getUsername())) {
                return ResultMsg.error("用户名不能为空");
            }

            // 查询用户
            User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getUsername, request.getUsername()));
            if (user == null) {
                return ResultMsg.error(ResultCode.USER_NOT_EXIST);
            }

            // 验证当前密码是否正确
            if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
                return ResultMsg.error("当前密码不正确");
            }

            // 检查新密码是否与当前密码相同
            if (passwordEncoder.matches(request.getNewPassword(), user.getPassword())) {
                return ResultMsg.error("新密码不能与当前密码相同");
            }

            // 加密新密码
            String encodedNewPassword = SecurityUtils.encryptPassword(passwordEncoder, request.getNewPassword());

            // 更新密码
            user.setPassword(encodedNewPassword);
            boolean success = userService.updateById(user);

            if (success) {
                return ResultMsg.success("密码修改成功");
            } else {
                return ResultMsg.error("密码修改失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            return ResultMsg.error("修改密码时发生异常：" + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    @GetMapping("/getUserInfo")
    public ResultMsg<User> getUserInfo(@RequestParam("username") String username) {
        try {
            // 验证username参数
            if (StringUtils.isBlank(username)) {
                return ResultMsg.error("用户名不能为空");
            }

            // 查询用户
            User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getUsername, username));
            if (user == null) {
                return ResultMsg.error(ResultCode.USER_NOT_EXIST);
            }

            // 清除敏感信息
            user.setPassword(null);

            // 获取用户角色信息
            List<SysRole> roles = userService.getUserRoles(user.getId());
            user.setRoles(roles);

            return ResultMsg.success(user);

        } catch (Exception e) {
            e.printStackTrace();
            return ResultMsg.error("获取用户信息时发生异常：" + e.getMessage());
        }
    }

    /**
     * 保存用户登录日志
     * @param username 用户名
     * @param request HTTP请求
     * @param success 是否登录成功
     * @param failReason 失败原因（如果登录失败）
     */
    private void saveLoginLog(String username, HttpServletRequest request, boolean success, String failReason) {
        try {
            JSONObject logJson = new JSONObject();
            logJson.put("username", username);
            logJson.put("loginTime", System.currentTimeMillis());
            logJson.put("loginIp", getIpAddress(request));
            logJson.put("loginSuccess", success);
            logJson.put("loginType", "PASSWORD"); // 密码登录
            logJson.put("userAgent", request.getHeader("User-Agent"));
            
            if (!success && failReason != null) {
                logJson.put("failReason", failReason);
            }
            userLoginLogDao.saveOrUpdate(logJson, UserLoginLogDao.COLLECTION_NAME_LOGIN_LOG);
        } catch (Exception e) {
            // 记录日志失败不应影响正常业务流程
            e.printStackTrace();
        }
    }
    /**
     * check-token接口，用于验证token是否有效
     * @return 验证结果
     */
    @GetMapping("/check-token")
    public ResultMsg<Boolean> checkToken(@RequestParam("token") String token) {
        // 验证token是否有效
        try {
            String username = jwtUtil.extractUsername(token);
            if (username == null) {
                return ResultMsg.error("token无效");
            }
            return ResultMsg.success(true);
        }catch (Exception e) {
            e.printStackTrace();
            return ResultMsg.error("token无效");
        }

    }

    
    /**
     * 获取客户端真实IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 对于通过多个代理的情况，第一个IP为客户端真实IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }
}
