# 用户修改昵称和密码接口说明

## 📋 新增接口概览

在 `UserController.java` 中新增了三个用户管理接口：

1. **修改昵称接口** - `PUT /user/updateNickname`
2. **修改密码接口** - `PUT /user/updatePassword`
3. **获取用户信息接口** - `GET /user/getUserInfo`

## ⚠️ 重要说明

**由于使用API Token登录方式，没有传统的登录状态，所以前端必须在请求中传递username参数。**

## 🔧 接口详细说明

### 1. 修改昵称接口

**接口地址**: `PUT /user/updateNickname`

**功能**: 允许指定用户修改昵称

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {API_TOKEN}
```

**请求体**:
```json
{
  "username": "用户名",
  "nickname": "新昵称"
}
```

**请求参数验证**:
- `username`: 必填，要修改的用户名
- `nickname`: 必填，长度1-50个字符

**响应示例**:
```json
{
  "code": 200,
  "message": "昵称修改成功",
  "data": null
}
```

**错误响应**:
```json
{
  "code": 400,
  "message": "用户名不能为空",
  "data": null
}
```

### 2. 修改密码接口

**接口地址**: `PUT /user/updatePassword`

**功能**: 允许指定用户修改密码

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {API_TOKEN}
```

**请求体**:
```json
{
  "username": "用户名",
  "currentPassword": "当前密码",
  "newPassword": "新密码",
  "confirmPassword": "确认新密码"
}
```

**请求参数验证**:
- `username`: 必填，要修改密码的用户名
- `currentPassword`: 必填，当前密码
- `newPassword`: 必填，长度6-20个字符
- `confirmPassword`: 必填，必须与新密码一致

**响应示例**:
```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": null
}
```

**错误响应**:
```json
{
  "code": 400,
  "message": "当前密码不正确",
  "data": null
}
```

### 3. 获取用户信息接口

**接口地址**: `GET /user/getUserInfo`

**功能**: 获取指定用户的详细信息

**请求头**:
```
Authorization: Bearer {API_TOKEN}
```

**请求参数**:
- `username`: 必填，要查询的用户名

**完整URL示例**: `GET /user/getUserInfo?username=testuser`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "createTime": "2025-06-30T10:00:00",
    "updateTime": "2025-06-30T15:30:00",
    "roles": [
      {
        "id": 1,
        "roleName": "普通用户",
        "roleCode": "USER"
      }
    ]
  }
}
```

## 🔒 安全特性

### 1. 身份验证
- 所有接口都需要API Token验证
- 通过username参数指定要操作的用户
- 前端需要确保传递正确的username

### 2. 密码安全
- 修改密码需要验证当前密码
- 新密码不能与当前密码相同
- 密码使用BCrypt加密存储
- 日志中不会记录明文密码

### 3. 数据验证
- 使用Jakarta Validation进行参数验证
- 昵称长度限制：1-50个字符
- 密码长度限制：6-20个字符
- 密码确认验证

### 4. 事务管理
- 所有修改操作都在事务中执行
- 发生异常时自动回滚
- 保证数据一致性

## 📝 前端集成示例

### JavaScript/Ajax 示例

```javascript
// 修改昵称
function updateNickname(username, newNickname) {
    $.ajax({
        url: '/user/updateNickname',
        type: 'PUT',
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('apiToken'),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({
            username: username,
            nickname: newNickname
        }),
        success: function(response) {
            if (response.code === 200) {
                alert('昵称修改成功！');
                // 刷新用户信息
                getUserInfo(username);
            } else {
                alert('修改失败：' + response.message);
            }
        },
        error: function() {
            alert('网络错误，请稍后重试');
        }
    });
}

// 修改密码
function updatePassword(username, currentPassword, newPassword, confirmPassword) {
    $.ajax({
        url: '/user/updatePassword',
        type: 'PUT',
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('apiToken'),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({
            username: username,
            currentPassword: currentPassword,
            newPassword: newPassword,
            confirmPassword: confirmPassword
        }),
        success: function(response) {
            if (response.code === 200) {
                alert('密码修改成功！');
                // 可能需要重新登录
            } else {
                alert('修改失败：' + response.message);
            }
        },
        error: function() {
            alert('网络错误，请稍后重试');
        }
    });
}

// 获取用户信息
function getUserInfo(username) {
    $.ajax({
        url: '/user/getUserInfo?username=' + encodeURIComponent(username),
        type: 'GET',
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('apiToken')
        },
        success: function(response) {
            if (response.code === 200) {
                const user = response.data;
                $('#username').text(user.username);
                $('#nickname').text(user.nickname);
                $('#email').text(user.email);
                // 更新页面上的用户信息
            }
        }
    });
}
```

### Vue.js 示例

```javascript
// Vue组件中的方法
export default {
  data() {
    return {
      username: 'testuser', // 当前操作的用户名
      user: {},
      nicknameForm: {
        username: 'testuser',
        nickname: ''
      },
      passwordForm: {
        username: 'testuser',
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    }
  },
  methods: {
    // 修改昵称
    async updateNickname() {
      try {
        const response = await this.$http.put('/user/updateNickname', this.nicknameForm);
        if (response.data.code === 200) {
          this.$message.success('昵称修改成功');
          this.getUserInfo();
        } else {
          this.$message.error(response.data.message);
        }
      } catch (error) {
        this.$message.error('网络错误');
      }
    },
    
    // 修改密码
    async updatePassword() {
      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
        this.$message.error('新密码和确认密码不一致');
        return;
      }
      
      try {
        const response = await this.$http.put('/user/updatePassword', this.passwordForm);
        if (response.data.code === 200) {
          this.$message.success('密码修改成功');
          // 清空表单
          this.passwordForm = {
            username: this.username,
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
          };
        } else {
          this.$message.error(response.data.message);
        }
      } catch (error) {
        this.$message.error('网络错误');
      }
    },
    
    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await this.$http.get(`/user/getUserInfo?username=${this.username}`);
        if (response.data.code === 200) {
          this.user = response.data.data;
          this.nicknameForm.nickname = this.user.nickname;
          this.nicknameForm.username = this.username;
        }
      } catch (error) {
        console.error('获取用户信息失败', error);
      }
    }
  },

  mounted() {
    this.getUserInfo();
  }
}
```

## ⚠️ 注意事项

1. **用户名传递**: 前端必须在每个请求中传递正确的username参数
2. **密码验证**: 修改密码时必须提供正确的当前密码
3. **数据验证**: 前端和后端都需要进行数据验证
4. **错误处理**: 需要妥善处理各种错误情况
5. **用户体验**: 修改成功后应该给用户明确的反馈
6. **API Token**: 使用API Token而不是传统的JWT登录令牌

## 🧪 测试

已创建完整的单元测试，包括：
- DTO类的验证测试
- 边界条件测试
- 安全性测试
- 数据完整性测试

运行测试：
```bash
mvn test -Dtest=UserControllerTest
```

所有测试用例都已通过 ✅

## 📊 总结

新增的用户修改接口提供了完整的用户信息管理功能：
- ✅ 修改昵称功能
- ✅ 修改密码功能
- ✅ 获取用户信息功能
- ✅ 完整的安全验证
- ✅ 事务管理
- ✅ 数据验证
- ✅ 错误处理
- ✅ 单元测试

这些接口可以直接用于前端开发，提供良好的用户体验和安全保障。
