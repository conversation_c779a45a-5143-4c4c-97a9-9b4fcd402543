package com.xnlpgyl.gift.Gift.User.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.xnlpgyl.gift.Gift.SysAuthorization.service.ISysAuthorizationService;
import com.xnlpgyl.gift.Gift.SysRole.entity.SysRole;
import com.xnlpgyl.gift.Gift.SysRole.service.ISysRoleService;
import com.xnlpgyl.gift.Gift.SysUserRole.entity.SysUserRole;
import com.xnlpgyl.gift.Gift.SysUserRole.service.ISysUserRoleService;
import com.xnlpgyl.gift.Gift.User.entity.User;
import com.xnlpgyl.gift.Gift.User.mapper.UserMapper;
import com.xnlpgyl.gift.Gift.User.service.IUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xnlpgyl.gift.Gift.security.CheckAuthentication;
import com.xnlpgyl.gift.Gift.security.LoginService;
import com.xnlpgyl.gift.Gift.security.LoginUser;
import com.xnlpgyl.gift.Gift.utils.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {
    public static final Map<String, Integer> loginFailMap = new ConcurrentHashMap<>();
    public static final Map<String, Long> loginErrorTimeMap = new ConcurrentHashMap<>();
    public static final Map<String, Long> lockMap = new ConcurrentHashMap<>();
    // 验证码失败计数器
    public static final Map<String, Integer> captchaFailMap = new ConcurrentHashMap<>();
    public static final Map<String, Long> captchaErrorTimeMap = new ConcurrentHashMap<>();
    private static final int MAX__LOGIN_FAIL = 10;
    private static final int MAX_CAPTCHA_FAIL = 5;
    private static final int LOCK_TIME = 3600000;
    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private LoginService loginService;
    @Autowired
    private ISysAuthorizationService sysAuthorizationService;

    @Autowired
    private AuthenticationManager authenticationManager;


    @Value("${jwt.expiration}")
    private Long expiration;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ISysUserRoleService sysUserRoleService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED)
    public JSONObject userLogin(String username, String password) throws Exception{
        //登录失败超过10次 锁定账号
        if(lockMap.containsKey(username)&&lockMap.get(username)>System.currentTimeMillis()){
            throw new Exception("账号被锁定，请稍后再试");
        }
        lockMap.remove(username);
        UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(username,password);
        Authentication authentication =   authenticationManager.authenticate(token);
        // 在 userLogin 方法中修改登录失败计数逻辑
        if (authentication == null || !authentication.isAuthenticated()) {
            // 使用 compute 原子操作替代非线程安全操作
            loginErrorTimeMap.put(username, System.currentTimeMillis());
            loginFailMap.compute(username, (k, v) -> v == null ? 1 : v + 1);

            if (loginFailMap.get(username) >= MAX__LOGIN_FAIL) {
                lockMap.put(username, System.currentTimeMillis() + LOCK_TIME);
                loginFailMap.remove(username);
                loginErrorTimeMap.remove(username);
                throw new Exception("账号被锁定");
            }
            throw new UsernameNotFoundException("登录失败");
        }
        //登录成功 重置登录失败次数和验证码失败次数
        loginFailMap.remove(username);
        loginErrorTimeMap.remove(username);
        lockMap.remove(username);
        clearCaptchaFailRecord(username);
        //登录成功 赋予对应权限
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        fillUserRoleAndAuthorities(loginUser);
        //将权限信息存储起来
        SecurityContextHolder.getContext().setAuthentication(authentication);
        CheckAuthentication.loginUserMap.put(username,loginUser);
        String tokenStr = jwtUtil.generateToken(loginService.loadUserByUsername(username));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Authorization","Bearer "+tokenStr);
        jsonObject.put("username",username);
        jsonObject.put("password",password);
        jsonObject.put("userId",loginUser.getUser().getId());
        jsonObject.put("nickName",loginUser.getUser().getNickname());
        jsonObject.put("expiration(ms)",expiration*1000);
        return jsonObject;
    }

    @Override
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    public LoginUser loadLoginUserByUsername(String username) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername,username);
        User user = getOne(queryWrapper);
        if(user!=null){
            LoginUser loginUser = new LoginUser(user);
            fillUserRoleAndAuthorities(loginUser);
            return loginUser;
        }
        return null;
    }

    @Override
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    public List<SysRole> getRolesByUserId(Long userId) {
        if (userId != null) {
            // 根据用户ID查询角色信息
            return userMapper.getRolesByUserId(userId);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED)
    public void saveRole(Long userId, List<Long> roleIds) {
        sysUserRoleService.remove(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        for(Long roleId:roleIds){
            sysUserRoleService.save(new SysUserRole(userId,roleId));
        }
    }

    @Override
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    public List<SysRole> getUserRoles(Long id) {
        List<SysUserRole> sysUserRoles = sysUserRoleService.list(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, id));
        if(sysUserRoles.isEmpty()){
            return null;
        }
       return sysRoleService.list(new LambdaQueryWrapper<SysRole>().in(SysRole::getId,sysUserRoles.stream().map(SysUserRole::getRoleId).toList()));
    }

    //为用户填充权限
    private void fillUserRoleAndAuthorities(LoginUser loginUser){
        //获取用户角色 根据角色获取权限
        //设置角色
        User user = loginUser.getUser();
        List<SysRole> roles = getRolesByUserId(user.getId());
        if(roles.isEmpty()){
            return;
        }
        user.setRoles(roles);
        for(SysRole role:roles){
            //设置权限
            Set<String> sysAuthorizations =sysAuthorizationService.getAuthoritiesByRoleId(role.getId());
            if(sysAuthorizations.isEmpty()){
                continue;
            }
            loginUser.setAuthoritiesList(sysAuthorizations);
        }
        //设置用户数据权限
        //todo
    }

    /**
     * 检查验证码失败次数是否超过限制
     * @param username 用户名
     * @return true表示超过限制，false表示未超过
     */
    public static boolean isCaptchaFailExceedLimit(String username) {
        Integer failCount = captchaFailMap.get(username);
        return failCount != null && failCount >= MAX_CAPTCHA_FAIL;
    }

    /**
     * 记录验证码失败
     * @param username 用户名
     */
    public static void recordCaptchaFail(String username) {
        captchaErrorTimeMap.put(username, System.currentTimeMillis());
        captchaFailMap.compute(username, (k, v) -> v == null ? 1 : v + 1);
    }

    /**
     * 清除验证码失败记录（登录成功时调用）
     * @param username 用户名
     */
    public static void clearCaptchaFailRecord(String username) {
        captchaFailMap.remove(username);
        captchaErrorTimeMap.remove(username);
    }

    /**
     * 获取验证码失败次数
     * @param username 用户名
     * @return 失败次数
     */
    public static int getCaptchaFailCount(String username) {
        return captchaFailMap.getOrDefault(username, 0);
    }
}
