package com.xnlpgyl.gift.Gift.User.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xnlpgyl.gift.Gift.SysRole.entity.SysRole;
import com.xnlpgyl.gift.Gift.config.BasePojo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Getter
@Setter
@TableName("user")
public class User extends BasePojo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String captcha; // 验证码字段
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 状态：0-禁用，1-启用 2-微信小程序登录
     */
    @TableField("status")
    private Byte status;

    /**
     * 角色列表
     */
    @TableField(exist = false)
    private List<SysRole> roles;

    public String toString() {
        return "User{" +
        "id=" + id +
        ", username=" + username +
        ", password=" + password +
        ", nickname=" + nickname +
        ", email=" + email +
        ", phone=" + phone +
        ", status=" + status +
        "}";
    }


}
