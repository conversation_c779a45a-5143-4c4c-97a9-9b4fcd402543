package com.xnlpgyl.gift.Gift.system;

import com.alibaba.fastjson2.JSONObject;
import com.xnlpgyl.gift.Gift.SysRole.entity.SysRole;
import com.xnlpgyl.gift.Gift.User.entity.User;
import com.xnlpgyl.gift.Gift.User.service.IUserService;
import com.xnlpgyl.gift.Gift.utils.AuthInitConfig;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 系统全局配置相关信息
 * <AUTHOR>
 *
 */
@Slf4j
@RestController
@RequestMapping("/System/bootstrap")
public class SystemBootStrapController {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private IUserService userService;
    @GetMapping("/getOpenApiList")
    public ResultMsg<JSONObject> index(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg", AuthInitConfig.menus);
        return ResultMsg.success(jsonObject);
    }
    /**
     *  从mongodb数据库里面的MenuList集合里面获取指定角色的菜单列表
     */
    @GetMapping("/getMenuListByRoleId/{roleId}")
    public ResultMsg<JSONObject> getMenuListByRoleId(@PathVariable long roleId){
        Query query = new Query(Criteria.where("roleId").is(roleId));
        JSONObject returnInfo = mongoTemplate.findOne(query, JSONObject.class,"MenuList");
        return ResultMsg.success(returnInfo);
    }

    /**
     * 通过用户ID获取菜单信息
     * @param userId
     * @return
     */
    @GetMapping("/getMenuListByUserId/{userId}")
    public ResultMsg<List<JSONObject>> getMenuListByUserId(@PathVariable long userId){
        List<SysRole> roleList =  userService.getRolesByUserId(userId);
        if (roleList == null){
            return ResultMsg.error("无任何权限");
        }
        List<Long> roleIds = roleList.stream().map(SysRole::getId).toList();
        Query query = new Query(Criteria.where("roleId").in (roleIds));
        List<JSONObject> returnInfo = mongoTemplate.find(query, JSONObject.class,"MenuList");
        return ResultMsg.success(returnInfo);
    }
    /**
     * 保存角色菜单信息
     */
    @PostMapping("/saveMenuListByRoleId")
    public ResultMsg<JSONObject> saveMenuListByRoleId(@RequestBody JSONObject jsonObject){
        //根据roleId查询是否存在，如果不存在则新增,否则更新
        Query query = new Query(Criteria.where("roleId").is(jsonObject.getLong("roleId")));
        JSONObject returnInfo = mongoTemplate.findOne(query, JSONObject.class,"MenuList");
        if(returnInfo!= null){
            jsonObject.put("_id", returnInfo.getLong("_id"));
        }
        if(!jsonObject.containsKey("_id")){
            jsonObject.put("_id", System.currentTimeMillis());
        }
        mongoTemplate.save(jsonObject, "MenuList");
        return ResultMsg.success(jsonObject);
    }
    /**
     *  从mongodb数据库里面的ApiList集合里面获取指定角色的Api权限列表
     */
    @GetMapping("/getApiListByRoleId/{roleId}")
    public ResultMsg<JSONObject> getApiListByRoleId(@PathVariable long roleId){
        Query query = new Query(Criteria.where("roleId").is(roleId));
        JSONObject returnInfo = mongoTemplate.findOne(query, JSONObject.class,"ApiList");
        return ResultMsg.success(returnInfo);
    }
    /**
     * 保存角色API接口信息
     */
    @PostMapping("/saveApiListByRoleId")
    public ResultMsg<JSONObject> saveApiListByRoleId(@RequestBody JSONObject jsonObject){
        //根据roleId查询是否存在，如果不存在则新增,否则更新
        Query query = new Query(Criteria.where("roleId").is(jsonObject.getLong("roleId")));
        JSONObject returnInfo = mongoTemplate.findOne(query, JSONObject.class,"ApiList");
        if(returnInfo!= null){
            jsonObject.put("_id", returnInfo.getLong("_id"));
        }
        if(!jsonObject.containsKey("_id")){
            jsonObject.put("_id", System.currentTimeMillis());
        }
        mongoTemplate.save(jsonObject, "ApiList");
        return ResultMsg.success(jsonObject);
    }
}
