package com.xnlpgyl.gift.Gift.websocket.manager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket 连接管理器
 * 管理所有的 WebSocket 连接
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Component
public class WebSocketConnectionManager {
    
    /**
     * 存储用户ID与WebSocket会话的映射关系
     * Key: 用户ID, Value: WebSocket会话
     */
    private final Map<String, WebSocketSession> userSessions = new ConcurrentHashMap<>();
    
    /**
     * 存储会话ID与用户ID的映射关系
     * Key: 会话ID, Value: 用户ID
     */
    private final Map<String, String> sessionUsers = new ConcurrentHashMap<>();
    
    /**
     * 存储用户连接时间
     * Key: 用户ID, Value: 连接时间
     */
    private final Map<String, LocalDateTime> userConnectTimes = new ConcurrentHashMap<>();
    
    /**
     * 添加连接
     */
    public void addConnection(String userId, WebSocketSession session) {
        // 如果用户已经有连接，先关闭旧连接
        removeConnection(userId);
        
        userSessions.put(userId, session);
        sessionUsers.put(session.getId(), userId);
        userConnectTimes.put(userId, LocalDateTime.now());
        
        log.info("用户 {} 建立WebSocket连接，会话ID: {}", userId, session.getId());
    }
    
    /**
     * 移除连接
     */
    public void removeConnection(String userId) {
        WebSocketSession session = userSessions.remove(userId);
        if (session != null) {
            sessionUsers.remove(session.getId());
            userConnectTimes.remove(userId);
            
            try {
                if (session.isOpen()) {
                    session.close();
                }
            } catch (Exception e) {
                log.error("关闭WebSocket连接失败", e);
            }
            
            log.info("用户 {} 的WebSocket连接已移除", userId);
        }
    }
    
    /**
     * 根据会话ID移除连接
     */
    public void removeConnectionBySessionId(String sessionId) {
        String userId = sessionUsers.remove(sessionId);
        if (userId != null) {
            userSessions.remove(userId);
            userConnectTimes.remove(userId);
            log.info("会话 {} 对应的用户 {} 连接已移除", sessionId, userId);
        }
    }
    
    /**
     * 获取用户的WebSocket会话
     */
    public WebSocketSession getSession(String userId) {
        return userSessions.get(userId);
    }
    
    /**
     * 根据会话ID获取用户ID
     */
    public String getUserId(String sessionId) {
        return sessionUsers.get(sessionId);
    }
    
    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(String userId) {
        WebSocketSession session = userSessions.get(userId);
        return session != null && session.isOpen();
    }
    
    /**
     * 获取所有在线用户ID
     */
    public Set<String> getOnlineUserIds() {
        Set<String> onlineUsers = new HashSet<>();
        userSessions.forEach((userId, session) -> {
            if (session.isOpen()) {
                onlineUsers.add(userId);
            }
        });
        return onlineUsers;
    }
    
    /**
     * 获取所有在线用户的会话
     */
    public Collection<WebSocketSession> getAllSessions() {
        return userSessions.values().stream()
                .filter(WebSocketSession::isOpen)
                .toList();
    }
    
    /**
     * 获取在线用户数量
     */
    public int getOnlineUserCount() {
        return (int) userSessions.values().stream()
                .filter(WebSocketSession::isOpen)
                .count();
    }
    
    /**
     * 获取用户连接时间
     */
    public LocalDateTime getUserConnectTime(String userId) {
        return userConnectTimes.get(userId);
    }
    
    /**
     * 清理无效连接
     */
    public void cleanInvalidConnections() {
        List<String> invalidUsers = new ArrayList<>();
        
        userSessions.forEach((userId, session) -> {
            if (!session.isOpen()) {
                invalidUsers.add(userId);
            }
        });
        
        invalidUsers.forEach(this::removeConnection);
        
        if (!invalidUsers.isEmpty()) {
            log.info("清理了 {} 个无效连接", invalidUsers.size());
        }
    }
    
    /**
     * 获取连接统计信息
     */
    public Map<String, Object> getConnectionStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalConnections", userSessions.size());
        stats.put("onlineUsers", getOnlineUserCount());
        stats.put("onlineUserIds", getOnlineUserIds());
        return stats;
    }
}
