package com.xnlpgyl.gift.Gift.websocket.enums;

/**
 * WebSocket 消息类型枚举
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
public enum MessageType {
    
    /**
     * 连接成功
     */
    CONNECT("connect", "连接成功"),
    
    /**
     * 断开连接
     */
    DISCONNECT("disconnect", "断开连接"),
    
    /**
     * 心跳检测
     */
    HEARTBEAT("heartbeat", "心跳检测"),
    
    /**
     * 单发消息
     */
    PRIVATE_MESSAGE("private_message", "单发消息"),
    
    /**
     * 群发消息
     */
    BROADCAST_MESSAGE("broadcast_message", "群发消息"),
    
    /**
     * 系统通知
     */
    SYSTEM_NOTIFICATION("system_notification", "系统通知"),
    
    /**
     * 在线用户列表
     */
    ONLINE_USERS("online_users", "在线用户列表"),
    
    /**
     * 错误消息
     */
    ERROR("error", "错误消息");
    
    private final String code;
    private final String description;
    
    MessageType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取消息类型
     */
    public static MessageType fromCode(String code) {
        for (MessageType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
