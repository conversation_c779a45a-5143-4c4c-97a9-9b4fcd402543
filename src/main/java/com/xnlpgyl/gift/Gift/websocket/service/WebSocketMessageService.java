package com.xnlpgyl.gift.Gift.websocket.service;

import com.alibaba.fastjson2.JSON;
import com.xnlpgyl.gift.Gift.websocket.dto.WebSocketMessage;
import com.xnlpgyl.gift.Gift.websocket.enums.MessageType;
import com.xnlpgyl.gift.Gift.websocket.manager.WebSocketConnectionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.util.Collection;
import java.util.Set;
import java.util.UUID;

/**
 * WebSocket 消息服务
 * 提供消息发送的业务逻辑
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Service
public class WebSocketMessageService {
    
    @Autowired
    private WebSocketConnectionManager connectionManager;
    
    /**
     * 发送消息给指定用户
     * 
     * @param userId 用户ID
     * @param message 消息内容
     * @return 是否发送成功
     */
    public boolean sendMessageToUser(String userId, WebSocketMessage message) {
        WebSocketSession session = connectionManager.getSession(userId);
        
        if (session == null || !session.isOpen()) {
            log.warn("用户 {} 不在线，无法发送消息", userId);
            return false;
        }
        
        try {
            // 设置消息ID
            if (message.getMessageId() == null) {
                message.setMessageId(UUID.randomUUID().toString());
            }
            
            // 设置时间戳
            if (message.getTimestamp() == null) {
                message.setTimestamp(java.time.LocalDateTime.now());
            }
            
            String messageJson = JSON.toJSONString(message);
            session.sendMessage(new TextMessage(messageJson));
            
            log.info("发送消息给用户 {}: {}", userId, messageJson);
            return true;
            
        } catch (Exception e) {
            log.error("发送消息给用户 {} 失败", userId, e);
            return false;
        }
    }
    
    /**
     * 广播消息给所有在线用户
     * 
     * @param message 消息内容
     * @return 成功发送的用户数量
     */
    public int broadcastMessage(WebSocketMessage message) {
        Collection<WebSocketSession> sessions = connectionManager.getAllSessions();
        
        if (sessions.isEmpty()) {
            log.info("没有在线用户，无法广播消息");
            return 0;
        }
        
        // 设置消息ID
        if (message.getMessageId() == null) {
            message.setMessageId(UUID.randomUUID().toString());
        }
        
        // 设置时间戳
        if (message.getTimestamp() == null) {
            message.setTimestamp(java.time.LocalDateTime.now());
        }
        
        String messageJson = JSON.toJSONString(message);
        int successCount = 0;
        
        for (WebSocketSession session : sessions) {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(messageJson));
                    successCount++;
                }
            } catch (Exception e) {
                String userId = connectionManager.getUserId(session.getId());
                log.error("广播消息给用户 {} 失败", userId, e);
            }
        }
        
        log.info("广播消息给 {} 个用户: {}", successCount, messageJson);
        return successCount;
    }
    
    /**
     * 发送消息给指定的多个用户
     * 
     * @param userIds 用户ID列表
     * @param message 消息内容
     * @return 成功发送的用户数量
     */
    public int sendMessageToUsers(Set<String> userIds, WebSocketMessage message) {
        if (userIds == null || userIds.isEmpty()) {
            log.warn("用户ID列表为空，无法发送消息");
            return 0;
        }
        
        // 设置消息ID
        if (message.getMessageId() == null) {
            message.setMessageId(UUID.randomUUID().toString());
        }
        
        // 设置时间戳
        if (message.getTimestamp() == null) {
            message.setTimestamp(java.time.LocalDateTime.now());
        }
        
        int successCount = 0;
        for (String userId : userIds) {
            if (sendMessageToUser(userId, message)) {
                successCount++;
            }
        }
        
        log.info("发送消息给 {} 个指定用户，成功 {} 个", userIds.size(), successCount);
        return successCount;
    }
    
    /**
     * 发送系统通知
     * 
     * @param userId 用户ID，如果为null则广播给所有用户
     * @param content 通知内容
     * @return 是否发送成功
     */
    public boolean sendSystemNotification(String userId, String content) {
        WebSocketMessage message = new WebSocketMessage();
        message.setType(MessageType.SYSTEM_NOTIFICATION);
        message.setFromUserId("system");
        message.setContent(content);
        message.setTimestamp(java.time.LocalDateTime.now());
        
        if (userId != null) {
            message.setToUserId(userId);
            return sendMessageToUser(userId, message);
        } else {
            return broadcastMessage(message) > 0;
        }
    }
    
    /**
     * 发送在线用户列表
     * 
     * @param userId 请求用户ID
     */
    public void sendOnlineUserList(String userId) {
        Set<String> onlineUsers = connectionManager.getOnlineUserIds();
        
        WebSocketMessage message = new WebSocketMessage();
        message.setType(MessageType.ONLINE_USERS);
        message.setFromUserId("system");
        message.setToUserId(userId);
        message.setContent(onlineUsers);
        message.setTimestamp(java.time.LocalDateTime.now());
        
        sendMessageToUser(userId, message);
    }
    
    /**
     * 检查用户是否在线
     * 
     * @param userId 用户ID
     * @return 是否在线
     */
    public boolean isUserOnline(String userId) {
        return connectionManager.isUserOnline(userId);
    }
    
    /**
     * 获取在线用户数量
     * 
     * @return 在线用户数量
     */
    public int getOnlineUserCount() {
        return connectionManager.getOnlineUserCount();
    }
    
    /**
     * 获取所有在线用户ID
     * 
     * @return 在线用户ID集合
     */
    public Set<String> getOnlineUserIds() {
        return connectionManager.getOnlineUserIds();
    }
    
    /**
     * 强制断开用户连接
     * 
     * @param userId 用户ID
     * @param reason 断开原因
     */
    public void disconnectUser(String userId, String reason) {
        // 发送断开通知
        WebSocketMessage disconnectMessage = WebSocketMessage.createDisconnectMessage(userId);
        disconnectMessage.setContent(reason);
        sendMessageToUser(userId, disconnectMessage);
        
        // 移除连接
        connectionManager.removeConnection(userId);
        
        log.info("强制断开用户 {} 的连接，原因: {}", userId, reason);
    }
}
