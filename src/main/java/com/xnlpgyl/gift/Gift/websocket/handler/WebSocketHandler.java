package com.xnlpgyl.gift.Gift.websocket.handler;

import com.alibaba.fastjson2.JSON;
import com.xnlpgyl.gift.Gift.websocket.dto.WebSocketMessage;
import com.xnlpgyl.gift.Gift.websocket.enums.MessageType;
import com.xnlpgyl.gift.Gift.websocket.manager.WebSocketConnectionManager;
import com.xnlpgyl.gift.Gift.websocket.service.WebSocketMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

/**
 * WebSocket 处理器
 * 处理 WebSocket 连接的建立、断开和消息收发
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Component
public class WebSocketHandler implements org.springframework.web.socket.WebSocketHandler {
    
    @Autowired
    private WebSocketConnectionManager connectionManager;
    
    @Autowired
    private WebSocketMessageService messageService;
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String userId = (String) session.getAttributes().get("userId");
        String username = (String) session.getAttributes().get("username");
        String clientIp = (String) session.getAttributes().get("clientIp");

        // 由于在拦截器中已经验证过，这里的用户信息应该是可信的
        if (userId == null || userId.trim().isEmpty()) {
            log.warn("WebSocket连接缺少用户ID，关闭连接");
            session.close(CloseStatus.BAD_DATA.withReason("身份验证失败"));
            return;
        }

        // 添加连接到管理器
        connectionManager.addConnection(userId, session);

        // 发送连接成功消息
        WebSocketMessage connectMessage = WebSocketMessage.createConnectMessage(userId);
        connectMessage.setContent("WebSocket连接成功，欢迎 " + username);
        messageService.sendMessageToUser(userId, connectMessage);

        // 广播用户上线通知（可选）
        WebSocketMessage onlineNotification = new WebSocketMessage();
        onlineNotification.setType(MessageType.SYSTEM_NOTIFICATION);
        onlineNotification.setFromUserId("system");
        onlineNotification.setContent("用户 " + username + " 已上线");
        onlineNotification.setTimestamp(java.time.LocalDateTime.now());

        // 可以选择是否广播用户上线通知
        // messageService.broadcastMessage(onlineNotification);

        log.info("用户 {} (ID: {}) 从IP {} 建立WebSocket连接成功", username, userId, clientIp);
    }
    
    @Override
    public void handleMessage(WebSocketSession session, org.springframework.web.socket.WebSocketMessage<?> message) throws Exception {
        String userId = connectionManager.getUserId(session.getId());
        
        if (userId == null) {
            log.warn("收到未知会话的消息: {}", session.getId());
            return;
        }
        
        try {
            // 解析消息
            String messageText = null;
            if (message instanceof TextMessage textMessage) {
                messageText = textMessage.getPayload();
            } else if (message instanceof BinaryMessage binaryMessage) {
                messageText = new String(binaryMessage.getPayload().array());
            }

            if (messageText == null || messageText.trim().isEmpty()) {
                log.warn("收到空消息，用户: {}", userId);
                return;
            }

            log.info("收到用户 {} 的消息: {}", userId, messageText);

            // 解析JSON消息
            WebSocketMessage wsMessage;
            try {
                wsMessage = JSON.parseObject(messageText, WebSocketMessage.class);
            } catch (Exception e) {
                log.error("解析消息JSON失败: {}", messageText, e);
                WebSocketMessage errorMessage = WebSocketMessage.createErrorMessage(userId, "消息格式错误");
                messageService.sendMessageToUser(userId, errorMessage);
                return;
            }

            // 设置发送者
            wsMessage.setFromUserId(userId);
            wsMessage.setTimestamp(java.time.LocalDateTime.now());

            // 处理不同类型的消息
            handleMessageByType(wsMessage);
            
        } catch (Exception e) {
            log.error("处理WebSocket消息失败，用户: {}", userId, e);
            WebSocketMessage errorMessage = WebSocketMessage.createErrorMessage(userId, "消息处理失败");
            messageService.sendMessageToUser(userId, errorMessage);
        }
    }
    
    /**
     * 根据消息类型处理消息
     */
    private void handleMessageByType(WebSocketMessage wsMessage) {
        switch (wsMessage.getType()) {
            case HEARTBEAT:
                // 心跳检测
                handleHeartbeat(wsMessage);
                break;
            case PRIVATE_MESSAGE:
                // 私聊消息
                handlePrivateMessage(wsMessage);
                break;
            case BROADCAST_MESSAGE:
                // 广播消息
                handleBroadcastMessage(wsMessage);
                break;
            default:
                log.warn("未知的消息类型: {}", wsMessage.getType());
                break;
        }
    }
    
    /**
     * 处理心跳消息
     */
    private void handleHeartbeat(WebSocketMessage wsMessage) {
        WebSocketMessage heartbeatResponse = WebSocketMessage.createHeartbeatMessage();
        messageService.sendMessageToUser(wsMessage.getFromUserId(), heartbeatResponse);
    }
    
    /**
     * 处理私聊消息
     */
    private void handlePrivateMessage(WebSocketMessage wsMessage) {
        String toUserId = wsMessage.getToUserId();
        if (toUserId == null || toUserId.trim().isEmpty()) {
            WebSocketMessage errorMessage = WebSocketMessage.createErrorMessage(
                wsMessage.getFromUserId(), "私聊消息缺少接收者");
            messageService.sendMessageToUser(wsMessage.getFromUserId(), errorMessage);
            return;
        }
        
        // 发送私聊消息
        boolean sent = messageService.sendMessageToUser(toUserId, wsMessage);
        
        // 给发送者发送状态反馈
        WebSocketMessage statusMessage = new WebSocketMessage();
        statusMessage.setType(MessageType.SYSTEM_NOTIFICATION);
        statusMessage.setFromUserId("system");
        statusMessage.setToUserId(wsMessage.getFromUserId());
        statusMessage.setContent(sent ? "消息发送成功" : "接收者不在线");
        statusMessage.setTimestamp(java.time.LocalDateTime.now());
        
        messageService.sendMessageToUser(wsMessage.getFromUserId(), statusMessage);
    }
    
    /**
     * 处理广播消息
     */
    private void handleBroadcastMessage(WebSocketMessage wsMessage) {
        messageService.broadcastMessage(wsMessage);
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String userId = connectionManager.getUserId(session.getId());
        log.error("WebSocket传输错误，用户: {}", userId, exception);
        
        if (userId != null) {
            connectionManager.removeConnection(userId);
        }
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String userId = connectionManager.getUserId(session.getId());
        
        if (userId != null) {
            connectionManager.removeConnectionBySessionId(session.getId());
            
            // 广播用户下线通知（可选）
            WebSocketMessage offlineNotification = new WebSocketMessage();
            offlineNotification.setType(MessageType.SYSTEM_NOTIFICATION);
            offlineNotification.setFromUserId("system");
            offlineNotification.setContent("用户 " + userId + " 已下线");
            offlineNotification.setTimestamp(java.time.LocalDateTime.now());
            
            // 可以选择是否广播用户下线通知
            // messageService.broadcastMessage(offlineNotification);
            
            log.info("用户 {} 断开WebSocket连接，状态: {}", userId, closeStatus);
        }
    }
    
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
}
