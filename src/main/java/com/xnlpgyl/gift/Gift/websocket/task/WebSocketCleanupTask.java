package com.xnlpgyl.gift.Gift.websocket.task;

import com.xnlpgyl.gift.Gift.websocket.manager.WebSocketConnectionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * WebSocket 清理定时任务
 * 定期清理无效的 WebSocket 连接
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Component
public class WebSocketCleanupTask {
    
    @Autowired
    private WebSocketConnectionManager connectionManager;
    
    /**
     * 每5分钟清理一次无效连接
     */
    @Scheduled(fixedRate = 5 * 60 * 1000) // 5分钟
    public void cleanupInvalidConnections() {
        try {
            log.debug("开始清理无效的WebSocket连接");
            connectionManager.cleanInvalidConnections();
            log.debug("WebSocket连接清理完成");
        } catch (Exception e) {
            log.error("清理WebSocket连接时发生错误", e);
        }
    }
    
    /**
     * 每小时打印连接统计信息
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时
    public void printConnectionStats() {
        try {
            var stats = connectionManager.getConnectionStats();
            log.info("WebSocket连接统计: {}", stats);
        } catch (Exception e) {
            log.error("打印WebSocket连接统计时发生错误", e);
        }
    }
}
