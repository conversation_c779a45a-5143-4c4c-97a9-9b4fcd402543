package com.xnlpgyl.gift.Gift.websocket.interceptor;

import com.xnlpgyl.gift.Gift.security.LoginUser;
import com.xnlpgyl.gift.Gift.security.LoginService;
import com.xnlpgyl.gift.Gift.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.net.URI;
import java.util.List;
import java.util.Map;

/**
 * WebSocket 握手拦截器
 * 在 WebSocket 连接建立前进行身份验证和参数提取
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Component
public class WebSocketInterceptor implements HandshakeInterceptor {

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private LoginService loginService;

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {

        String path = request.getURI().getPath();
        log.info("WebSocket握手请求路径: {}", path);

        try {
            // 1. 从查询参数或请求头中获取 token
            String token = extractToken(request);
            if (token == null || token.trim().isEmpty() ||!token.trim().toLowerCase().startsWith("bearer ")) {
                log.warn("WebSocket连接缺少token，拒绝连接");
                return false;
            }
            token = token.trim().replace("Bearer ", "");
            if (token.trim().isEmpty()) {
                log.warn("WebSocket连接缺少token，拒绝连接");
                return false;
            }

            // 2. 验证 token 并获取用户信息
            String username = jwtUtil.extractUsername(token);
            if (username == null || username.trim().isEmpty()) {
                log.warn("WebSocket连接token无效，拒绝连接");
                return false;
            }

            // 3. 加载用户详细信息
            LoginUser userDetails = loginService.loadUserByUsername(username);
            if (userDetails == null) {
                log.warn("WebSocket连接用户不存在: {}", username);
                return false;
            }

            // 4. 验证 token 有效性
            if (!jwtUtil.validateToken(token, userDetails)) {
                log.warn("WebSocket连接token验证失败: {}", username);
                return false;
            }

            // 5. 从查询参数中提取请求的用户ID（可选，用于验证）
            String requestedUserId = extractUserIdFromQuery(request);

            // 6. 验证用户是否有权限连接到指定的用户ID（可选）
            // 如果查询参数中指定了userId，验证是否与token中的用户匹配
            if (requestedUserId != null && !requestedUserId.equals(userDetails.getUser().getId().toString())) {
                log.warn("WebSocket连接用户ID不匹配 - token用户: {}, 请求用户: {}",
                        userDetails.getUser().getId(), requestedUserId);
                // 这里可以选择拒绝连接或者忽略查询参数中的userId
                // return false; // 严格模式：拒绝连接
            }

            // 7. 将验证后的用户信息存储到会话属性中
            attributes.put("userId", userDetails.getUser().getId().toString());
            attributes.put("username", username);
            attributes.put("userDetails", userDetails);
            attributes.put("token", token);
            if (requestedUserId != null) {
                attributes.put("requestedUserId", requestedUserId);
            }

            // 获取客户端IP
            String clientIp = getClientIp(request);
            attributes.put("clientIp", clientIp);

            log.info("WebSocket连接验证成功 - 用户: {}, ID: {}, IP: {}",
                    username, userDetails.getUser().getId(), clientIp);

            return true;

        } catch (Exception e) {
            log.error("WebSocket握手验证失败", e);
            return false;
        }
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        if (exception != null) {
            log.error("WebSocket握手失败", exception);
        } else {
            log.info("WebSocket握手成功");
        }
    }

    /**
     * 从请求中提取 token
     * 支持多种方式：查询参数、请求头
     */
    private String extractToken(ServerHttpRequest request) {
        // 1. 从查询参数中获取 token
        URI uri = request.getURI();
        String query = uri.getQuery();
        if (query != null) {
            String[] params = query.split("&");
            for (String param : params) {
                String[] keyValue = param.split("=");
                if (keyValue.length == 2 && "token".equals(keyValue[0])) {
                    return keyValue[1];
                }
            }
        }

        // 2. 从请求头中获取 token
        List<String> authHeaders = request.getHeaders().get("Authorization");
        if (authHeaders != null && !authHeaders.isEmpty()) {
            String authHeader = authHeaders.get(0);
            if (authHeader.startsWith("Bearer ")) {
                return authHeader.substring(7);
            }
        }

        // 3. 从 Sec-WebSocket-Protocol 头中获取 token（WebSocket 特有方式）
        List<String> protocols = request.getHeaders().get("Sec-WebSocket-Protocol");
        if (protocols != null && !protocols.isEmpty()) {
            for (String protocol : protocols) {
                if (protocol.startsWith("token-")) {
                    return protocol.substring(6); // 移除 "token-" 前缀
                }
            }
        }

        return null;
    }

    /**
     * 从查询参数中提取用户ID
     */
    private String extractUserIdFromQuery(ServerHttpRequest request) {
        URI uri = request.getURI();
        String query = uri.getQuery();
        if (query != null) {
            String[] params = query.split("&");
            for (String param : params) {
                String[] keyValue = param.split("=");
                if (keyValue.length == 2 && "userId".equals(keyValue[0])) {
                    return keyValue[1];
                }
            }
        }
        return null;
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }

        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        request.getRemoteAddress();
        return request.getRemoteAddress().getAddress().getHostAddress();
    }
}
