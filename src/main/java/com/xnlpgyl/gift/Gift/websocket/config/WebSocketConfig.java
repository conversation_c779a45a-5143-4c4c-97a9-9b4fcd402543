package com.xnlpgyl.gift.Gift.websocket.config;

import com.xnlpgyl.gift.Gift.websocket.handler.WebSocketHandler;
import com.xnlpgyl.gift.Gift.websocket.interceptor.WebSocketInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket 配置类
 * 配置 WebSocket 端点和拦截器
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private WebSocketHandler webSocketHandler;
    
    @Autowired
    private WebSocketInterceptor webSocketInterceptor;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        log.info("=== 注册WebSocket处理器 ===");

        // 注册 WebSocket 处理器 - 原生 WebSocket
        // 用户身份通过查询参数中的 token 验证，不使用路径变量
        log.info("注册WebSocket端点: /websocket");
        registry.addHandler(webSocketHandler, "/websocket")
                .addInterceptors(webSocketInterceptor)
                .setAllowedOrigins("*") // 允许所有来源
                .setAllowedOriginPatterns("*"); // 支持更灵活的跨域

        log.info("WebSocket处理器注册完成");
    }
}
