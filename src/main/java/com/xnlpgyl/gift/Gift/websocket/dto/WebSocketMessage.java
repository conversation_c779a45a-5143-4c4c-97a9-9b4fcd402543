package com.xnlpgyl.gift.Gift.websocket.dto;

import com.xnlpgyl.gift.Gift.websocket.enums.MessageType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * WebSocket 消息传输对象
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketMessage {
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 消息类型
     */
    private MessageType type;
    
    /**
     * 发送者ID
     */
    private String fromUserId;
    
    /**
     * 接收者ID（单发消息时使用）
     */
    private String toUserId;
    
    /**
     * 消息内容
     */
    private Object content;
    
    /**
     * 消息时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 额外数据
     */
    private Object extra;
    
    /**
     * 创建连接成功消息
     */
    public static WebSocketMessage createConnectMessage(String userId) {
        WebSocketMessage message = new WebSocketMessage();
        message.setType(MessageType.CONNECT);
        message.setFromUserId("system");
        message.setToUserId(userId);
        message.setContent("连接成功");
        message.setTimestamp(LocalDateTime.now());
        return message;
    }
    
    /**
     * 创建断开连接消息
     */
    public static WebSocketMessage createDisconnectMessage(String userId) {
        WebSocketMessage message = new WebSocketMessage();
        message.setType(MessageType.DISCONNECT);
        message.setFromUserId("system");
        message.setToUserId(userId);
        message.setContent("连接已断开");
        message.setTimestamp(LocalDateTime.now());
        return message;
    }
    
    /**
     * 创建心跳消息
     */
    public static WebSocketMessage createHeartbeatMessage() {
        WebSocketMessage message = new WebSocketMessage();
        message.setType(MessageType.HEARTBEAT);
        message.setFromUserId("system");
        message.setContent("pong");
        message.setTimestamp(LocalDateTime.now());
        return message;
    }
    
    /**
     * 创建错误消息
     */
    public static WebSocketMessage createErrorMessage(String userId, String errorMsg) {
        WebSocketMessage message = new WebSocketMessage();
        message.setType(MessageType.ERROR);
        message.setFromUserId("system");
        message.setToUserId(userId);
        message.setContent(errorMsg);
        message.setTimestamp(LocalDateTime.now());
        return message;
    }
    
    /**
     * 创建私聊消息
     */
    public static WebSocketMessage createPrivateMessage(String fromUserId, String toUserId, Object content) {
        WebSocketMessage message = new WebSocketMessage();
        message.setType(MessageType.PRIVATE_MESSAGE);
        message.setFromUserId(fromUserId);
        message.setToUserId(toUserId);
        message.setContent(content);
        message.setTimestamp(LocalDateTime.now());
        return message;
    }
    
    /**
     * 创建广播消息
     */
    public static WebSocketMessage createBroadcastMessage(String fromUserId, Object content) {
        WebSocketMessage message = new WebSocketMessage();
        message.setType(MessageType.BROADCAST_MESSAGE);
        message.setFromUserId(fromUserId);
        message.setContent(content);
        message.setTimestamp(LocalDateTime.now());
        return message;
    }
}
