package com.xnlpgyl.gift.Gift.websocket.controller;

import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import com.xnlpgyl.gift.Gift.websocket.dto.SendMessageRequest;
import com.xnlpgyl.gift.Gift.websocket.dto.WebSocketMessage;
import com.xnlpgyl.gift.Gift.websocket.enums.MessageType;
import com.xnlpgyl.gift.Gift.websocket.manager.WebSocketConnectionManager;
import com.xnlpgyl.gift.Gift.websocket.service.WebSocketMessageService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Set;

/**
 * WebSocket REST API 控制器
 * 提供 WebSocket 相关的 HTTP 接口
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@RestController
@RequestMapping("/websocket")
public class WebSocketController {
    
    @Autowired
    private WebSocketMessageService messageService;
    
    @Autowired
    private WebSocketConnectionManager connectionManager;
    
    /**
     * 发送消息
     *
     * @param request 发送消息请求
     * @return 发送结果
     */
    @PostMapping("/send")
    public ResultMsg<Boolean> sendMessage(@Valid @RequestBody SendMessageRequest request) {
        try {
            WebSocketMessage message = new WebSocketMessage();
            message.setFromUserId(request.getFromUserId());
            message.setContent(request.getContent());
            message.setExtra(request.getExtra());
            
            boolean success = false;
            
            if ("private".equals(request.getMessageType())) {
                // 私聊消息
                if (request.getToUserId() == null || request.getToUserId().trim().isEmpty()) {
                    return ResultMsg.error("私聊消息缺少接收者用户ID");
                }
                
                message.setType(MessageType.PRIVATE_MESSAGE);
                message.setToUserId(request.getToUserId());
                success = messageService.sendMessageToUser(request.getToUserId(), message);
                
            } else if ("broadcast".equals(request.getMessageType())) {
                // 广播消息
                message.setType(MessageType.BROADCAST_MESSAGE);
                int sentCount = messageService.broadcastMessage(message);
                success = sentCount > 0;
                
            } else {
                return ResultMsg.error("不支持的消息类型: " + request.getMessageType());
            }
            
            if (success) {
                return ResultMsg.success("消息发送成功");
            } else {
                return ResultMsg.error("消息发送失败");
            }
            
        } catch (Exception e) {
            log.error("发送消息失败", e);
            return ResultMsg.error("发送消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送系统通知
     * 
     * @param userId 用户ID，为空则广播给所有用户
     * @param content 通知内容
     * @return 发送结果
     */
    @PostMapping("/notification")
    public ResultMsg<Boolean> sendNotification(@RequestParam(required = false) String userId,
                                               @RequestParam String content) {
        try {
            boolean success = messageService.sendSystemNotification(userId, content);
            
            if (success) {
                return ResultMsg.success("通知发送成功");
            } else {
                return ResultMsg.error("通知发送失败");
            }
            
        } catch (Exception e) {
            log.error("发送通知失败", e);
            return ResultMsg.error("发送通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取在线用户列表
     * 
     * @return 在线用户列表
     */
    @GetMapping("/online-users")
    public ResultMsg<Set<String>> getOnlineUsers() {
        try {
            Set<String> onlineUsers = messageService.getOnlineUserIds();
            return ResultMsg.success(onlineUsers);
            
        } catch (Exception e) {
            log.error("获取在线用户列表失败", e);
            return ResultMsg.error("获取在线用户列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查用户是否在线
     * 
     * @param userId 用户ID
     * @return 是否在线
     */
    @GetMapping("/online-status/{userId}")
    public ResultMsg<Boolean> checkUserOnline(@PathVariable String userId) {
        try {
            boolean isOnline = messageService.isUserOnline(userId);
            return ResultMsg.success(isOnline);
            
        } catch (Exception e) {
            log.error("检查用户在线状态失败", e);
            return ResultMsg.error("检查用户在线状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取连接统计信息
     * 
     * @return 连接统计信息
     */
    @GetMapping("/stats")
    public ResultMsg<Map<String, Object>> getConnectionStats() {
        try {
            Map<String, Object> stats = connectionManager.getConnectionStats();
            return ResultMsg.success(stats);
            
        } catch (Exception e) {
            log.error("获取连接统计信息失败", e);
            return ResultMsg.error("获取连接统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 强制断开用户连接
     * 
     * @param userId 用户ID
     * @param reason 断开原因
     * @return 操作结果
     */
    @PostMapping("/disconnect/{userId}")
    public ResultMsg<Boolean> disconnectUser(@PathVariable String userId,
                                             @RequestParam(defaultValue = "管理员强制断开") String reason) {
        try {
            messageService.disconnectUser(userId, reason);
            return ResultMsg.success("用户连接已断开");
            
        } catch (Exception e) {
            log.error("断开用户连接失败", e);
            return ResultMsg.error("断开用户连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理无效连接
     * 
     * @return 操作结果
     */
    @PostMapping("/clean-connections")
    public ResultMsg<Boolean> cleanInvalidConnections() {
        try {
            connectionManager.cleanInvalidConnections();
            return ResultMsg.success("无效连接清理完成");
            
        } catch (Exception e) {
            log.error("清理无效连接失败", e);
            return ResultMsg.error("清理无效连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送在线用户列表给指定用户
     * 
     * @param userId 用户ID
     * @return 操作结果
     */
    @PostMapping("/send-online-users/{userId}")
    public ResultMsg<Boolean> sendOnlineUserList(@PathVariable String userId) {
        try {
            messageService.sendOnlineUserList(userId);
            return ResultMsg.success("在线用户列表已发送");
            
        } catch (Exception e) {
            log.error("发送在线用户列表失败", e);
            return ResultMsg.error("发送在线用户列表失败: " + e.getMessage());
        }
    }
}
