package com.xnlpgyl.gift.Gift.websocket.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发送消息请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SendMessageRequest {
    
    /**
     * 发送者用户ID
     */
    @NotBlank(message = "发送者用户ID不能为空")
    private String fromUserId;
    
    /**
     * 接收者用户ID（单发消息时必填）
     */
    private String toUserId;
    
    /**
     * 消息内容
     */
    @NotNull(message = "消息内容不能为空")
    private Object content;
    
    /**
     * 消息类型：private（私聊）或 broadcast（广播）
     */
    @NotBlank(message = "消息类型不能为空")
    private String messageType;
    
    /**
     * 额外数据
     */
    private Object extra;
}
