package com.xnlpgyl.gift.Gift.SysRole.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xnlpgyl.gift.Gift.SysRole.entity.SysRole;
import com.xnlpgyl.gift.Gift.SysRole.service.ISysRoleService;
import com.xnlpgyl.gift.Gift.config.BaseController;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 系统用户角色表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
@RestController
@RequestMapping("/SysRole/sysRole")
public class SysRoleController extends BaseController {
    @Autowired
    private ISysRoleService sysRoleService;

    public SysRoleController(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }
    //基本增删改查方法
    @PostMapping("/getListByPage")
    public ResultMsg<Page<SysRole>> getListByPage(@RequestBody JSONObject pageInfo) {
        System.out.println(pageInfo);
        Page<SysRole> page = Page.of(pageInfo.getInteger("current"), pageInfo.getInteger("size"));
        page = sysRoleService.page(page);
        return ResultMsg.success(page);
    }
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public ResultMsg<SysRole> add(@RequestBody SysRole sysRole) {
        sysRoleService.saveOrUpdate(sysRole);
        return ResultMsg.success();
    }
    @PostMapping("/update")
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED,isolation = Isolation.READ_COMMITTED)
    public ResultMsg<SysRole> update(@RequestBody SysRole sysRole) {
        sysRoleService.updateById(sysRole);
        return ResultMsg.success();
    }
    @DeleteMapping("/{id}")
    public ResultMsg<SysRole> delete(@PathVariable("id") String id) {
        sysRoleService.removeById(id);
        return ResultMsg.success();
    }
    @GetMapping("/getAllRoleList")
    public ResultMsg<JSONObject> getAllRoleList() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("roleList", sysRoleService.list());
        return ResultMsg.success(jsonObject);
    }
}
