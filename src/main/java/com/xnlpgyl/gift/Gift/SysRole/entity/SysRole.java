package com.xnlpgyl.gift.Gift.SysRole.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xnlpgyl.gift.Gift.config.BasePojo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统用户角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
@Getter
@Setter
@ToString
@TableName("t_sys_role")
public class SysRole extends BasePojo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色标识符
     */
    @TableField("roleKey")
    private String roleKey;

    /**
     * 角色名称
     */
    @TableField("name")
    private String name;

    /**
     * 角色简介
     */
    @TableField("description")
    private String description;
}
