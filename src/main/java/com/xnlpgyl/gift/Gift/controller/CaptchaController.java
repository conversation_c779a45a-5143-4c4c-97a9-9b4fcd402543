package com.xnlpgyl.gift.Gift.controller;

import java.io.*;

import cn.hutool.core.codec.Base64;
import com.xnlpgyl.gift.Gift.utils.ResultMsg;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import com.xnlpgyl.gift.Gift.utils.MathCaptchaValidator;
import java.awt.image.BufferedImage;
import java.nio.charset.StandardCharsets;
import javax.imageio.ImageIO;

/**
 * 验证码控制器
 */
@RestController
public class CaptchaController {
    /**
     * 生成并输出数学表达式验证码图片
     * @param request Web请求对象
     * @throws IOException 如果发生IO异常
     */
    @GetMapping("/captcha")
    public ResultMsg<String> generateCaptcha(HttpServletRequest request) throws IOException {
        // 生成数学表达式和答案
        String[] captcha = MathCaptchaValidator.generateMathCaptcha();
        
        // 将数学表达式存储在session中以便后续验证使用
        request.getSession().setAttribute("captchaAnswer", captcha[1]);
        // 生成验证码图片
        BufferedImage captchaImage = MathCaptchaValidator.generateCaptchaImage(captcha[0]);
        String pic = Base64.encode(imageToBytes(captchaImage));
        return ResultMsg.success(pic);
    }
    /**
     * 将BufferedImage对象转换为字节数组
     * @param captchaImage 验证码BufferedImage对象
     * @return 图像的字节数组
     */
    private byte[] imageToBytes(BufferedImage captchaImage) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            // 将图片写入字节数组输出流
            ImageIO.write(captchaImage, "png", outputStream);
            // 获取字节数组
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0];
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}