server:
  port: 8080

spring:
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  data:
    mongodb:
      uri: **************************************************************************************
    redis:
      host: *************
      port: 6379
      password: xnlpgyl123 # 如果有密码，请取消注释并设置
      database: 0 # 使用的数据库索引，默认是0
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************
    username: root
    password: xnlpgyl123
    hikari:
      # 连接池名
      pool-name: HikariCP
      # 最小空闲连接数
      minimum-idle: 5
      # 空闲连接存活最大时间，默认600000（10分钟）
      idle-timeout: 180000
      # 最大连接数，默认10
      maximum-pool-size: 10
      # 从连接池返回的连接的自动提交
      auto-commit: true
      # 连接最大存活时间，0表示永久存活，默认1800000（30分钟）
      max-lifetime: 1800000
      # 连接超时时间，默认30000（30秒）
      connection-timeout: 30000
      # 测试连接是否可用的查询语句
      connection-test-query: SELECT 1

mybatis-plus:
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:/mapper/**/*.xml
  # 搜索指定包别名
  type-aliases-package: com.xnlpgyl.gift.Gift.entity
  # 配置mybatis数据返回类型别名（默认别名是类名）
  type-aliases-super-type: java.lang.Object
  configuration:
    # 驼峰命名
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: false
    # 允许JDBC生成主键
    use-generated-keys: true
    # 配置默认的执行器
    default-executor-type: REUSE
    # 指定返回结果的默认类型
    default-result-set-type: DEFAULT
    # 使用驼峰命名转换原始列名
    use-column-label: true
  global-config:
    db-config:
      # 全局默认主键类型
      id-type: AUTO
      # 逻辑已删除值
      logic-delete-value: true
      # 逻辑未删除值
      logic-not-delete-value: false
      # 逻辑删除字段名
      logic-delete-field-name: deleted
    banner: false
file:
  upload:
    path: /var/www/data
    url: https://www.xnlpgyl.com/api/gift/file/upload
    max-size: 104857600
    allow-types: jpg,jpeg,png,gif,ico,bmp
  download:
    url: https://www.xnlpgyl.com/api/gift/file/download/

# 微信小程序登录配置
weixin:
  miniapp:
    # 微信小程序配置
    app-id: wxae95026f0450c13e  # 小程序AppID
    app-secret: d09c8a47f3dd366f13452e23735337d8  # 小程序AppSecret

    # 小程序登录相关URL配置
    login-url: https://api.weixin.qq.com/sns/jscode2session
    access-token-url: https://api.weixin.qq.com/cgi-bin/token
    phone-number-url: https://api.weixin.qq.com/wxa/business/getuserphonenumber
    grant-type: authorization_code