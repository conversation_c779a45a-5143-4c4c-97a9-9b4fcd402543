<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xnlpgyl.gift.Gift.User.mapper.UserMapper">
    <select id="getRolesByUserId" resultType="com.xnlpgyl.gift.Gift.SysRole.entity.SysRole">
        select * from t_sys_role where id in (select role_id from t_sys_user_role where user_id = #{userId})
    </select>
</mapper>
