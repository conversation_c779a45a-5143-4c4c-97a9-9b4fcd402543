<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xnlpgyl.gift.Gift.SysAuthorization.mapper.SysAuthorizationMapper">

    <select id="getAuthoritiesByRoleId"
            resultType="com.xnlpgyl.gift.Gift.SysAuthorization.entity.SysAuthorization">
        select * from t_sys_authorization where id in (select auth_id from t_sys_role_authorization where role_id = #{id})
    </select>
</mapper>
