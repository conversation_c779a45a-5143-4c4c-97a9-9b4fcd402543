spring:
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB
      max-request-size: 500MB
  profiles:
    active: test
    include: request-log
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  data:
    mongodb:
      auto-index-creation: true
    redis:
      timeout: 10000 # 连接超时时间
      lettuce:
        pool:
          max-active: 8 # 连接池最大连接数
          max-idle: 8 # 连接池中的最大空闲连接
          min-idle: 0 # 连接池中的最小空闲连接
          max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
mvc:
  pathmatch:
    matching-strategy: ant_path_matcher

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: AUTO
jwt:
  secret: GbsfLkGUvh12hg3817ZhOZJY7N6BJXK1CjfmV0m59zBtDnoNk6ymClyA6sU8TsjO
  expiration: 259200
  header: Authorization
  tokenHead: Bearer
  tokenStartWith: "Bearer "
