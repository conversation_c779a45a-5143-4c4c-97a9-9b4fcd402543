<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .connect { background-color: #4CAF50; color: white; }
        .disconnect { background-color: #f44336; color: white; }
        .send { background-color: #008CBA; color: white; }
        #messages {
            border: 1px solid #ccc;
            height: 300px;
            overflow-y: auto;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .system { background-color: #e7f3ff; }
        .error { background-color: #ffe7e7; }
        .success { background-color: #e7ffe7; }
    </style>
</head>
<body>
    <h1>🔧 WebSocket 简单连接测试</h1>
    
    <div class="container">
        <h2>连接测试</h2>
        <p>这个页面用于测试WebSocket连接是否能够到达服务器</p>
        
        <button class="connect" onclick="testConnect()">测试连接 /websocket</button>
        <button class="disconnect" onclick="disconnect()">断开连接</button>
        
        <div id="status">未连接</div>
    </div>

    <div class="container">
        <h2>消息日志</h2>
        <div id="messages"></div>
        <button onclick="clearMessages()">清空日志</button>
    </div>

    <script>
        let websocket = null;
        let isConnected = false;

        function addMessage(content, type = 'system') {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageEl.innerHTML = `<strong>[${timestamp}]</strong> ${content}`;
            
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function testConnect() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                addMessage('已经连接，请先断开现有连接', 'error');
                return;
            }

            updateStatus('正在连接...');
            
            // 尝试连接到 /websocket 端点（不带任何参数）
            const wsUrl = `ws://localhost:8080/websocket`;
            
            addMessage(`尝试连接: ${wsUrl}`, 'system');
            
            try {
                websocket = new WebSocket(wsUrl);
                
                websocket.onopen = function(event) {
                    isConnected = true;
                    updateStatus('✅ 已连接');
                    addMessage('WebSocket连接成功！', 'success');
                };
                
                websocket.onmessage = function(event) {
                    addMessage(`收到消息: ${event.data}`, 'system');
                };
                
                websocket.onclose = function(event) {
                    isConnected = false;
                    updateStatus('❌ 连接已关闭');
                    addMessage(`连接关闭 - 代码: ${event.code}, 原因: ${event.reason}`, 'error');
                };
                
                websocket.onerror = function(error) {
                    updateStatus('❌ 连接错误');
                    addMessage(`连接错误: ${error}`, 'error');
                };
                
            } catch (error) {
                updateStatus('❌ 连接失败');
                addMessage(`连接异常: ${error.message}`, 'error');
            }
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
                isConnected = false;
                updateStatus('❌ 已断开');
                addMessage('主动断开连接', 'system');
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
    </script>
</body>
</html>
