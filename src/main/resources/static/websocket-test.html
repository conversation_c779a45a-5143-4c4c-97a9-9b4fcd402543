<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        input, button, textarea {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007bff;
            background-color: white;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <h1>WebSocket 连接测试</h1>
    
    <div class="container">
        <h3>连接配置</h3>
        <div>
            <label>服务器地址:</label>
            <input type="text" id="serverUrl" value="ws://localhost:8080/websocket" placeholder="ws://localhost:8080/websocket">
        </div>
        <div>
            <label>用户ID:</label>
            <input type="text" id="userId" value="testuser123" placeholder="用户ID">
        </div>
        <div>
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
        </div>
        <div id="status" class="status disconnected">未连接</div>
    </div>

    <div class="container">
        <h3>发送消息</h3>
        <div>
            <label>消息类型:</label>
            <select id="messageType">
                <option value="PRIVATE_MESSAGE">私聊消息</option>
                <option value="BROADCAST_MESSAGE">广播消息</option>
                <option value="HEARTBEAT">心跳</option>
            </select>
        </div>
        <div>
            <label>接收用户ID (私聊时填写):</label>
            <input type="text" id="toUserId" placeholder="接收用户ID">
        </div>
        <div>
            <label>消息内容:</label>
            <textarea id="messageContent" rows="3" cols="50" placeholder="输入消息内容"></textarea>
        </div>
        <div>
            <button id="sendBtn" onclick="sendMessage()" disabled>发送消息</button>
        </div>
    </div>

    <div class="container">
        <h3>消息日志</h3>
        <button onclick="clearMessages()">清空日志</button>
        <div id="messages"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function updateStatus(message, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + className;
        }

        function addMessage(message, isError = false) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message' + (isError ? ' error' : '');
            messageDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            const userId = document.getElementById('userId').value;
            
            if (!serverUrl || !userId) {
                addMessage('请填写服务器地址和用户ID', true);
                return;
            }

            const fullUrl = `${serverUrl}/${userId}`;
            addMessage(`尝试连接到: ${fullUrl}`);
            updateStatus('连接中...', 'connecting');

            try {
                ws = new WebSocket(fullUrl);

                ws.onopen = function(event) {
                    isConnected = true;
                    updateStatus('已连接', 'connected');
                    addMessage('WebSocket 连接成功');
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    document.getElementById('sendBtn').disabled = false;
                };

                ws.onmessage = function(event) {
                    addMessage(`收到消息: ${event.data}`);
                };

                ws.onerror = function(error) {
                    addMessage(`WebSocket 错误: ${error}`, true);
                    console.error('WebSocket error:', error);
                };

                ws.onclose = function(event) {
                    isConnected = false;
                    updateStatus('连接已关闭', 'disconnected');
                    addMessage(`连接关闭 - 代码: ${event.code}, 原因: ${event.reason || '未知'}`);
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    document.getElementById('sendBtn').disabled = true;
                };

            } catch (error) {
                addMessage(`连接失败: ${error.message}`, true);
                updateStatus('连接失败', 'disconnected');
            }
        }

        function disconnect() {
            if (ws && isConnected) {
                ws.close();
                addMessage('主动断开连接');
            }
        }

        function sendMessage() {
            if (!ws || !isConnected) {
                addMessage('WebSocket 未连接', true);
                return;
            }

            const messageType = document.getElementById('messageType').value;
            const toUserId = document.getElementById('toUserId').value;
            const content = document.getElementById('messageContent').value;

            if (!content.trim()) {
                addMessage('请输入消息内容', true);
                return;
            }

            const message = {
                type: messageType,
                content: content,
                fromUserId: document.getElementById('userId').value,
                timestamp: Date.now()
            };

            if (messageType === 'PRIVATE_MESSAGE' && toUserId) {
                message.toUserId = toUserId;
            }

            try {
                ws.send(JSON.stringify(message));
                addMessage(`发送消息: ${JSON.stringify(message, null, 2)}`);
                document.getElementById('messageContent').value = '';
            } catch (error) {
                addMessage(`发送失败: ${error.message}`, true);
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 页面加载时的初始化
        window.onload = function() {
            addMessage('WebSocket 测试页面已加载');
            
            // 尝试自动检测服务器地址
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const autoUrl = `${protocol}//${host}/websocket`;
            document.getElementById('serverUrl').value = autoUrl;
            
            addMessage(`自动检测的服务器地址: ${autoUrl}`);
        };

        // 页面关闭时清理连接
        window.onbeforeunload = function() {
            if (ws && isConnected) {
                ws.close();
            }
        };
    </script>
</body>
</html>
