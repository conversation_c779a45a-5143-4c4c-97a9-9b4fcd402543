<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 安全连接测试 - 前后端分离版本</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .message.sent {
            background-color: #e3f2fd;
            text-align: right;
        }
        .message.received {
            background-color: #f1f8e9;
        }
        .message.system {
            background-color: #fff3e0;
            font-style: italic;
        }
        .message.error {
            background-color: #ffebee;
            color: #c62828;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>🔐 WebSocket 安全连接测试 - 前后端分离版本</h1>
    
    <div class="container">
        <h2>📋 使用说明</h2>
        <p><strong>重要：</strong>此版本的 WebSocket 需要有效的 JWT Token 进行身份验证。</p>
        <ol>
            <li>首先通过登录接口获取 JWT Token</li>
            <li>将 Token 填入下方的 Token 输入框</li>
            <li>选择连接方式并点击连接</li>
            <li>连接成功后即可发送消息</li>
        </ol>
        <p><strong>支持的 Token 传递方式：</strong></p>
        <ul>
            <li>查询参数：<code>ws://localhost:8080/websocket?token=your_jwt_token&userId=123</code></li>
            <li>WebSocket 协议头：<code>Sec-WebSocket-Protocol: token-your_jwt_token</code></li>
        </ul>
    </div>

    <div class="grid">
        <div class="container">
            <h2>🔗 连接配置</h2>
            
            <div class="form-group">
                <label for="serverHost">服务器地址:</label>
                <input type="text" id="serverHost" value="localhost:8080" placeholder="例如: localhost:8080">
            </div>
            
            <div class="form-group">
                <label for="jwtToken">JWT Token (必填):</label>
                <textarea id="jwtToken" rows="3" placeholder="请输入从登录接口获取的 JWT Token"></textarea>
            </div>
            
            <div class="form-group">
                <label for="userId">用户ID (可选，用于验证):</label>
                <input type="text" id="userId" value="123" placeholder="用户ID，可选，用于验证身份">
            </div>
            
            <div class="form-group">
                <label for="connectionType">连接类型:</label>
                <select id="connectionType">
                    <option value="websocket">原生 WebSocket (/websocket)</option>
                    <option value="sockjs">SockJS (/websocket-sockjs)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="tokenMethod">Token 传递方式:</label>
                <select id="tokenMethod">
                    <option value="query">查询参数 (?token=...)</option>
                    <option value="protocol">WebSocket 协议头</option>
                </select>
            </div>
            
            <button id="connectBtn" onclick="connect()">🔗 连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>❌ 断开</button>
            
            <div id="status" class="status disconnected">
                ❌ 未连接
            </div>
        </div>

        <div class="container">
            <h2>💬 消息发送</h2>
            
            <div class="form-group">
                <label for="messageType">消息类型:</label>
                <select id="messageType">
                    <option value="BROADCAST_MESSAGE">广播消息</option>
                    <option value="PRIVATE_MESSAGE">私聊消息</option>
                    <option value="HEARTBEAT">心跳检测</option>
                </select>
            </div>
            
            <div class="form-group" id="toUserGroup">
                <label for="toUserId">接收者ID (私聊时必填):</label>
                <input type="text" id="toUserId" placeholder="接收者用户ID">
            </div>
            
            <div class="form-group">
                <label for="messageContent">消息内容:</label>
                <textarea id="messageContent" rows="3" placeholder="输入消息内容"></textarea>
            </div>
            
            <button onclick="sendMessage()" disabled id="sendBtn">📤 发送消息</button>
            <button onclick="sendHeartbeat()" disabled id="heartbeatBtn">💓 发送心跳</button>
            <button onclick="clearMessages()">🗑️ 清空消息</button>
        </div>
    </div>

    <div class="container">
        <h2>📨 消息记录</h2>
        <div id="messages" class="messages"></div>
    </div>

    <div class="container">
        <h2>🔧 快速测试</h2>
        <button onclick="quickLogin()">🚀 快速获取测试 Token</button>
        <button onclick="testConnection()">🧪 测试连接</button>
        <button onclick="testBroadcast()">📢 测试广播</button>
        <button onclick="testPrivateMessage()">💌 测试私聊</button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script>
        let websocket = null;
        let isConnected = false;

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动检测服务器地址
            const host = window.location.host;
            document.getElementById('serverHost').value = host;
            
            // 监听消息类型变化
            document.getElementById('messageType').addEventListener('change', function() {
                const toUserGroup = document.getElementById('toUserGroup');
                if (this.value === 'PRIVATE_MESSAGE') {
                    toUserGroup.style.display = 'block';
                } else {
                    toUserGroup.style.display = 'none';
                }
            });
            
            // 初始化隐藏私聊用户输入
            document.getElementById('toUserGroup').style.display = 'none';
        });

        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function addMessage(content, type = 'system') {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageEl.innerHTML = `<strong>[${timestamp}]</strong> ${content}`;
            
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function connect() {
            const serverHost = document.getElementById('serverHost').value;
            const jwtToken = document.getElementById('jwtToken').value.trim();
            const userId = document.getElementById('userId').value || '123';
            const connectionType = document.getElementById('connectionType').value;
            const tokenMethod = document.getElementById('tokenMethod').value;
            
            if (!jwtToken) {
                alert('请输入 JWT Token！');
                return;
            }
            
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                addMessage('已经连接，请先断开现有连接', 'error');
                return;
            }
            
            updateStatus('🔄 正在连接...', 'connecting');
            
            // 构建 WebSocket URL
            let wsUrl;
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

            switch (connectionType) {
                case 'websocket':
                    wsUrl = `${protocol}//${serverHost}/websocket`;
                    break;
                case 'sockjs':
                    wsUrl = `http://${serverHost}/websocket-sockjs`;
                    break;
            }

            // 添加查询参数
            if (tokenMethod === 'query') {
                const params = new URLSearchParams();
                params.append('token', jwtToken);
                if (userId) {
                    params.append('userId', userId);
                }
                wsUrl += '?' + params.toString();
            }
            
            addMessage(`尝试连接: ${wsUrl}`, 'system');
            
            try {
                if (connectionType.includes('sockjs')) {
                    // 使用 SockJS
                    websocket = new SockJS(wsUrl);
                } else {
                    // 使用原生 WebSocket
                    if (tokenMethod === 'protocol') {
                        websocket = new WebSocket(wsUrl, ['token-' + jwtToken]);
                    } else {
                        websocket = new WebSocket(wsUrl);
                    }
                }
                
                websocket.onopen = function(event) {
                    isConnected = true;
                    updateStatus('✅ 已连接', 'connected');
                    addMessage('WebSocket 连接成功！', 'system');
                    
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    document.getElementById('sendBtn').disabled = false;
                    document.getElementById('heartbeatBtn').disabled = false;
                };
                
                websocket.onmessage = function(event) {
                    addMessage(`收到: ${event.data}`, 'received');
                };
                
                websocket.onclose = function(event) {
                    isConnected = false;
                    updateStatus('❌ 连接已关闭', 'disconnected');
                    addMessage(`连接关闭: ${event.code} - ${event.reason}`, 'system');
                    
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    document.getElementById('sendBtn').disabled = true;
                    document.getElementById('heartbeatBtn').disabled = true;
                };
                
                websocket.onerror = function(error) {
                    addMessage(`连接错误: ${error}`, 'error');
                    updateStatus('❌ 连接错误', 'disconnected');
                };
                
            } catch (error) {
                addMessage(`连接失败: ${error.message}`, 'error');
                updateStatus('❌ 连接失败', 'disconnected');
            }
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }

        function sendMessage() {
            if (!isConnected || !websocket) {
                addMessage('请先建立连接', 'error');
                return;
            }
            
            const messageType = document.getElementById('messageType').value;
            const content = document.getElementById('messageContent').value.trim();
            const toUserId = document.getElementById('toUserId').value.trim();
            
            if (!content) {
                addMessage('请输入消息内容', 'error');
                return;
            }
            
            if (messageType === 'PRIVATE_MESSAGE' && !toUserId) {
                addMessage('私聊消息请输入接收者ID', 'error');
                return;
            }
            
            const message = {
                type: messageType,
                content: content,
                toUserId: messageType === 'PRIVATE_MESSAGE' ? toUserId : null,
                timestamp: new Date().toISOString()
            };
            
            try {
                websocket.send(JSON.stringify(message));
                addMessage(`发送: ${JSON.stringify(message)}`, 'sent');
                document.getElementById('messageContent').value = '';
            } catch (error) {
                addMessage(`发送失败: ${error.message}`, 'error');
            }
        }

        function sendHeartbeat() {
            if (!isConnected || !websocket) {
                addMessage('请先建立连接', 'error');
                return;
            }
            
            const heartbeat = {
                type: 'HEARTBEAT',
                content: 'ping',
                timestamp: new Date().toISOString()
            };
            
            try {
                websocket.send(JSON.stringify(heartbeat));
                addMessage(`发送心跳: ${JSON.stringify(heartbeat)}`, 'sent');
            } catch (error) {
                addMessage(`心跳发送失败: ${error.message}`, 'error');
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 快速测试功能
        function quickLogin() {
            addMessage('请手动调用登录接口获取 JWT Token', 'system');
            addMessage('登录接口: POST /User/user/login', 'system');
            addMessage('参数: {"username": "your_username", "password": "your_password"}', 'system');
        }

        function testConnection() {
            if (!document.getElementById('jwtToken').value.trim()) {
                alert('请先输入 JWT Token');
                return;
            }
            connect();
        }

        function testBroadcast() {
            document.getElementById('messageType').value = 'BROADCAST_MESSAGE';
            document.getElementById('messageContent').value = '这是一条测试广播消息';
            sendMessage();
        }

        function testPrivateMessage() {
            document.getElementById('messageType').value = 'PRIVATE_MESSAGE';
            document.getElementById('toUserId').value = '456';
            document.getElementById('messageContent').value = '这是一条测试私聊消息';
            document.getElementById('toUserGroup').style.display = 'block';
            sendMessage();
        }
    </script>
</body>
</html>
