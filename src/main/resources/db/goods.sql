CREATE TABLE `gift`.`t_goods` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` VARCHAR(45) NOT NULL COMMENT '商品名称',
  `price` VARCHAR(45) NOT NULL COMMENT '商品价格',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  `update_time` DATETIME NOT NULL COMMENT '更新时间',
  `create_by` BIGINT NOT NULL COMMENT '创建人编号',
  `update_by` BIGINT NOT NULL COMMENT '更新人编号',
  `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标志位',
  `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `name_UNIQUE` (`name` ASC) VISIBLE)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci
COMMENT = '商品表';