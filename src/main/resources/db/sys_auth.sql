CREATE TABLE `gift`.`t_sys_authorization` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `auth_path` VARCHAR(45) NOT NULL COMMENT '权限路径',
  `auth_desc` VARCHAR(45) NOT NULL COMMENT '权限描述',
  `is_menu_auth` TINYINT NOT NULL COMMENT '是否是菜单权限',
  `parent_auth_path` VARCHAR(45) NOT NULL COMMENT '父权限路径',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  `update_time` DATETIME NOT NULL COMMENT '更新时间',
  `create_by` BIGINT NOT NULL COMMENT '创建人编号',
  `update_by` BIGINT NOT NULL COMMENT '更新人编号',
  `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标志位',
  `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
	PRIMARY KEY (`id`),
	UNIQUE INDEX `auth_path_UNIQUE` (`auth_path` ASC)
)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci
COMMENT = '系统用户权限表';