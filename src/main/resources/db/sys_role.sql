CREATE TABLE `gift`.`t_sys_role` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `roleKey` VARCHAR(45) NOT NULL COMMENT '角色标识符',
  `name` VARCHAR(45) NOT NULL COMMENT '角色名称',
  `description` VARCHAR(45)  COMMENT '角色简介',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  `update_time` DATETIME NOT NULL COMMENT '更新时间',
  `create_by` BIGINT NOT NULL COMMENT '创建人编号',
  `update_by` BIGINT NOT NULL COMMENT '更新人编号',
  `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标志位',
  `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
	PRIMARY KEY (`id`),
	UNIQUE INDEX `roleKey_UNIQUE` (`roleKey` ASC) ,
	UNIQUE INDEX `name_UNIQUE` (`name` ASC)  
)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci
COMMENT = '系统用户角色表';