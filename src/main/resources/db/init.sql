-- 创建数据库
CREATE DATABASE IF NOT EXISTS `gift` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用数据库
USE `gift`;

-- 创建用户并授权（如果需要）
-- CREATE USER 'gift_user'@'localhost' IDENTIFIED BY 'gift_password';
-- GRANT ALL PRIVILEGES ON gift.* TO 'gift_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 数据库初始化说明
/*
此脚本用于初始化gift数据库环境。

执行顺序：
1. 先执行此脚本创建数据库
2. 然后执行user.sql创建用户表

注意事项：
- 如果需要创建特定的数据库用户，请取消上面的注释并修改用户名和密码
- 默认使用utf8mb4字符集，支持完整的Unicode字符（包括emoji）
- 此脚本只需在首次部署时执行一次
*/