-- 创建用户表
CREATE TABLE `user` (
    -- 主键ID
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',

    -- 用户基本信息
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(100) NOT NULL COMMENT '密码',
    `nickname` VARCHAR(50) COMMENT '昵称',
    `email` VARCHAR(100) COMMENT '邮箱',
    `phone` VARCHAR(20) COMMENT '手机号',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',

    -- 基础字段
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `update_time` DATETIME NOT NULL COMMENT '更新时间',
    `create_by` BIGINT NOT NULL COMMENT '创建人',
    `update_by` BIGINT NOT NULL COMMENT '更新人',
    `deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',

    -- 索引
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    <PERSON>EY `idx_create_time` (`create_time`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 插入测试数据
INSERT INTO `user` (
    `username`,
    `password`,
    `nickname`,
    `email`,
    `phone`,
    `status`,
    `create_time`,
    `update_time`,
    `create_by`,
    `update_by`,
    `deleted`
) VALUES (
    'admin',
    '$2a$10$IIZeKbPGrGjXXgSFxNYXDuIJPWZKBBFhBJZwXm7MRCUTwB6zv.VTq', -- 密码：123456
    '管理员',
    '<EMAIL>',
    '13800138000',
    1,
    NOW(),
    NOW(),
    1,
    1,
    FALSE
);

-- 创建相关数据库函数（如果需要）
DELIMITER //

-- 获取用户昵称
CREATE FUNCTION IF NOT EXISTS get_user_nickname(user_id BIGINT)
RETURNS VARCHAR(50)
READS SQL DATA
BEGIN
    DECLARE nickname VARCHAR(50);
    SELECT u.nickname INTO nickname
    FROM user u
    WHERE u.id = user_id
    AND u.deleted = FALSE;
    RETURN IFNULL(nickname, '');
END //

DELIMITER ;

-- 数据库表说明
/*
用户表字段说明：
- id: 主键ID，自增长
- username: 用户名，唯一键，用于登录
- password: 密码，加密存储
- nickname: 昵称，显示名称
- email: 邮箱地址
- phone: 手机号
- status: 用户状态，0表示禁用，1表示启用
- create_time: 创建时间
- update_time: 更新时间
- create_by: 创建人ID
- update_by: 更新人ID
- deleted: 逻辑删除标记，true表示已删除，false表示未删除

索引说明：
- PRIMARY KEY: 主键索引
- uk_username: 用户名唯一索引
- idx_create_time: 创建时间索引，用于按时间查询和排序
- idx_status: 状态索引，用于按状态筛选

注意事项：
1. 密码字段使用加密存储，不允许明文存储
2. 使用逻辑删除，删除的记录不会真正从数据库中删除
3. 用户状态默认为1（启用）
4. 所有时间字段使用DATETIME类型，支持精确到秒的时间记录
*/