package com.xnlpgyl.gift.Gift.userAddress;

import com.xnlpgyl.gift.Gift.userAddress.entity.UserAddress;
import com.xnlpgyl.gift.Gift.userAddress.service.IUserAddressService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UserAddress服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class UserAddressServiceTest {

    @Autowired
    private IUserAddressService userAddressService;

    @Test
    public void testServiceNotNull() {
        System.out.println("[DEBUG_LOG] Testing userAddressService is not null");
        assertNotNull(userAddressService, "UserAddressService should not be null");
        System.out.println("[DEBUG_LOG] UserAddressService is properly injected");
    }

    @Test
    public void testCreateUserAddress() {
        System.out.println("[DEBUG_LOG] Testing create user address");
        
        // 创建测试地址
        UserAddress address = new UserAddress();
        address.setUserId(1L);
        address.setReceiverName("测试用户");
        address.setReceiverPhone("13800138000");
        address.setProvince("北京市");
        address.setCity("北京市");
        address.setDistrict("朝阳区");
        address.setDetailAddress("测试街道123号");
        address.setPostalCode("100000");
        address.setAddressLabel("家");
        
        System.out.println("[DEBUG_LOG] Created test address: " + address);
        
        // 验证地址对象创建成功
        assertNotNull(address);
        assertEquals("测试用户", address.getReceiverName());
        assertEquals("13800138000", address.getReceiverPhone());
        assertEquals("北京市", address.getProvince());
        assertEquals("北京市", address.getCity());
        assertEquals("朝阳区", address.getDistrict());
        assertEquals("测试街道123号", address.getDetailAddress());
        assertEquals("100000", address.getPostalCode());
        assertEquals("家", address.getAddressLabel());
        
        System.out.println("[DEBUG_LOG] User address creation test passed");
    }

    @Test
    public void testGetAddressesByUserId() {
        System.out.println("[DEBUG_LOG] Testing get addresses by user ID");
        
        Long testUserId = 1L;
        List<UserAddress> addresses = userAddressService.getAddressesByUserId(testUserId);
        
        System.out.println("[DEBUG_LOG] Retrieved addresses for user " + testUserId + ": " + addresses.size() + " addresses");
        
        // 验证返回结果不为null
        assertNotNull(addresses);
        
        System.out.println("[DEBUG_LOG] Get addresses by user ID test passed");
    }

    @Test
    public void testGetDefaultAddressByUserId() {
        System.out.println("[DEBUG_LOG] Testing get default address by user ID");
        
        Long testUserId = 1L;
        UserAddress defaultAddress = userAddressService.getDefaultAddressByUserId(testUserId);
        
        System.out.println("[DEBUG_LOG] Retrieved default address for user " + testUserId + ": " + defaultAddress);
        
        // 验证方法执行不抛异常（可能返回null，这是正常的）
        System.out.println("[DEBUG_LOG] Get default address by user ID test passed");
    }

    @Test
    public void testValidateAddressOwnership() {
        System.out.println("[DEBUG_LOG] Testing validate address ownership");
        
        // 测试null参数
        boolean result1 = userAddressService.validateAddressOwnership(null, 1L);
        assertFalse(result1, "Should return false for null address ID");
        
        boolean result2 = userAddressService.validateAddressOwnership(1L, null);
        assertFalse(result2, "Should return false for null user ID");
        
        boolean result3 = userAddressService.validateAddressOwnership(null, null);
        assertFalse(result3, "Should return false for both null parameters");
        
        System.out.println("[DEBUG_LOG] Validate address ownership test passed");
    }
}