package com.xnlpgyl.gift.Gift.websocket;

import com.xnlpgyl.gift.Gift.websocket.dto.WebSocketMessage;
import com.xnlpgyl.gift.Gift.websocket.enums.MessageType;
import com.xnlpgyl.gift.Gift.websocket.manager.WebSocketConnectionManager;
import com.xnlpgyl.gift.Gift.websocket.service.WebSocketMessageService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WebSocket 服务测试类
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@SpringBootTest
@ActiveProfiles("test")
public class WebSocketServiceTest {
    
    /**
     * 测试 WebSocketMessage 创建方法
     */
    @Test
    public void testWebSocketMessageCreation() {
        // 测试连接消息
        WebSocketMessage connectMessage = WebSocketMessage.createConnectMessage("user123");
        assertNotNull(connectMessage);
        assertEquals(MessageType.CONNECT, connectMessage.getType());
        assertEquals("user123", connectMessage.getToUserId());
        assertEquals("system", connectMessage.getFromUserId());
        assertEquals("连接成功", connectMessage.getContent());
        assertNotNull(connectMessage.getTimestamp());
        
        // 测试断开连接消息
        WebSocketMessage disconnectMessage = WebSocketMessage.createDisconnectMessage("user123");
        assertNotNull(disconnectMessage);
        assertEquals(MessageType.DISCONNECT, disconnectMessage.getType());
        assertEquals("user123", disconnectMessage.getToUserId());
        assertEquals("连接已断开", disconnectMessage.getContent());
        
        // 测试心跳消息
        WebSocketMessage heartbeatMessage = WebSocketMessage.createHeartbeatMessage();
        assertNotNull(heartbeatMessage);
        assertEquals(MessageType.HEARTBEAT, heartbeatMessage.getType());
        assertEquals("pong", heartbeatMessage.getContent());
        
        // 测试错误消息
        WebSocketMessage errorMessage = WebSocketMessage.createErrorMessage("user123", "测试错误");
        assertNotNull(errorMessage);
        assertEquals(MessageType.ERROR, errorMessage.getType());
        assertEquals("user123", errorMessage.getToUserId());
        assertEquals("测试错误", errorMessage.getContent());
        
        // 测试私聊消息
        WebSocketMessage privateMessage = WebSocketMessage.createPrivateMessage("user1", "user2", "Hello");
        assertNotNull(privateMessage);
        assertEquals(MessageType.PRIVATE_MESSAGE, privateMessage.getType());
        assertEquals("user1", privateMessage.getFromUserId());
        assertEquals("user2", privateMessage.getToUserId());
        assertEquals("Hello", privateMessage.getContent());
        
        // 测试广播消息
        WebSocketMessage broadcastMessage = WebSocketMessage.createBroadcastMessage("admin", "系统通知");
        assertNotNull(broadcastMessage);
        assertEquals(MessageType.BROADCAST_MESSAGE, broadcastMessage.getType());
        assertEquals("admin", broadcastMessage.getFromUserId());
        assertEquals("系统通知", broadcastMessage.getContent());
        
        System.out.println("WebSocketMessage创建方法测试通过");
    }
    
    /**
     * 测试 MessageType 枚举
     */
    @Test
    public void testMessageType() {
        // 测试枚举值
        assertEquals("connect", MessageType.CONNECT.getCode());
        assertEquals("连接成功", MessageType.CONNECT.getDescription());
        
        assertEquals("private_message", MessageType.PRIVATE_MESSAGE.getCode());
        assertEquals("单发消息", MessageType.PRIVATE_MESSAGE.getDescription());
        
        assertEquals("broadcast_message", MessageType.BROADCAST_MESSAGE.getCode());
        assertEquals("群发消息", MessageType.BROADCAST_MESSAGE.getDescription());
        
        // 测试 fromCode 方法
        assertEquals(MessageType.CONNECT, MessageType.fromCode("connect"));
        assertEquals(MessageType.PRIVATE_MESSAGE, MessageType.fromCode("private_message"));
        assertEquals(MessageType.BROADCAST_MESSAGE, MessageType.fromCode("broadcast_message"));
        assertNull(MessageType.fromCode("unknown"));
        
        System.out.println("MessageType枚举测试通过");
    }
    
    /**
     * 测试连接管理器基本功能
     */
    @Test
    public void testConnectionManager() {
        WebSocketConnectionManager manager = new WebSocketConnectionManager();
        
        // 测试初始状态
        assertEquals(0, manager.getOnlineUserCount());
        assertTrue(manager.getOnlineUserIds().isEmpty());
        assertFalse(manager.isUserOnline("user123"));
        
        // 测试统计信息
        var stats = manager.getConnectionStats();
        assertNotNull(stats);
        assertEquals(0, stats.get("totalConnections"));
        assertEquals(0, stats.get("onlineUsers"));
        
        System.out.println("ConnectionManager基本功能测试通过");
    }
    
    /**
     * 测试消息服务基本功能
     */
    @Test
    public void testMessageService() {
        // 创建连接管理器和消息服务
        WebSocketConnectionManager connectionManager = new WebSocketConnectionManager();
        WebSocketMessageService messageService = new WebSocketMessageService();

        // 手动设置依赖（在实际应用中由Spring自动注入）
        // 这里我们通过反射设置私有字段来模拟依赖注入
        try {
            var field = WebSocketMessageService.class.getDeclaredField("connectionManager");
            field.setAccessible(true);
            field.set(messageService, connectionManager);
        } catch (Exception e) {
            // 如果反射失败，跳过这个测试
            System.out.println("MessageService测试跳过（依赖注入模拟失败）");
            return;
        }

        // 测试获取在线用户（无连接时）
        assertEquals(0, messageService.getOnlineUserCount());
        assertTrue(messageService.getOnlineUserIds().isEmpty());
        assertFalse(messageService.isUserOnline("user123"));

        System.out.println("MessageService基本功能测试通过");
    }
    
    /**
     * 测试DTO验证
     */
    @Test
    public void testDTOValidation() {
        // 测试WebSocketMessage的getter/setter
        WebSocketMessage message = new WebSocketMessage();
        message.setMessageId("msg123");
        message.setType(MessageType.PRIVATE_MESSAGE);
        message.setFromUserId("user1");
        message.setToUserId("user2");
        message.setContent("Hello World");
        
        assertEquals("msg123", message.getMessageId());
        assertEquals(MessageType.PRIVATE_MESSAGE, message.getType());
        assertEquals("user1", message.getFromUserId());
        assertEquals("user2", message.getToUserId());
        assertEquals("Hello World", message.getContent());
        
        // 测试toString方法
        String toString = message.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("msg123"));
        assertTrue(toString.contains("user1"));
        assertTrue(toString.contains("user2"));
        
        System.out.println("DTO验证测试通过");
    }
    
    /**
     * 测试边界条件
     */
    @Test
    public void testBoundaryConditions() {
        // 测试null值处理
        WebSocketMessage message = new WebSocketMessage();
        assertNull(message.getMessageId());
        assertNull(message.getType());
        assertNull(message.getFromUserId());
        assertNull(message.getToUserId());
        assertNull(message.getContent());
        
        // 测试空字符串
        message.setFromUserId("");
        message.setToUserId("");
        assertEquals("", message.getFromUserId());
        assertEquals("", message.getToUserId());
        
        System.out.println("边界条件测试通过");
    }
}
