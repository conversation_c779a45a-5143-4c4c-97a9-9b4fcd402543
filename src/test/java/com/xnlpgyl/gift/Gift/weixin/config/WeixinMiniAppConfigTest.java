package com.xnlpgyl.gift.Gift.weixin.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 微信小程序配置测试类
 * 
 * <AUTHOR>
 * @since 2025-06-29
 */
public class WeixinMiniAppConfigTest {
    
    private WeixinMiniAppConfig config;
    
    @BeforeEach
    public void setUp() {
        config = new WeixinMiniAppConfig();
    }
    
    /**
     * 测试默认配置
     */
    @Test
    public void testDefaultConfig() {
        assertEquals("wxae95026f0450c13e", config.getAppId(), "默认AppID应该正确");
        assertEquals("d09c8a47f3dd366f13452e23735337d8", config.getAppSecret(), "默认AppSecret应该正确");
        assertEquals("https://api.weixin.qq.com/sns/jscode2session", config.getLoginUrl(), "登录URL应该正确");
        assertEquals("authorization_code", config.getGrantType(), "授权类型应该正确");
        
        System.out.println("小程序配置验证通过:");
        System.out.println("AppID: " + config.getAppId());
        System.out.println("登录URL: " + config.getLoginUrl());
    }
    
    /**
     * 测试构建登录URL
     */
    @Test
    public void testBuildLoginUrl() {
        String testCode = "test_js_code_123";
        String loginUrl = config.buildLoginUrl(testCode);
        
        assertNotNull(loginUrl, "登录URL不应该为空");
        assertTrue(loginUrl.contains(config.getAppId()), "URL应该包含AppID");
        assertTrue(loginUrl.contains(config.getAppSecret()), "URL应该包含AppSecret");
        assertTrue(loginUrl.contains(testCode), "URL应该包含js_code");
        assertTrue(loginUrl.contains("grant_type=authorization_code"), "URL应该包含授权类型");
        
        System.out.println("构建的登录URL: " + loginUrl);
    }
    
    /**
     * 测试构建access_token URL
     */
    @Test
    public void testBuildAccessTokenUrl() {
        String accessTokenUrl = config.buildAccessTokenUrl();
        
        assertNotNull(accessTokenUrl, "access_token URL不应该为空");
        assertTrue(accessTokenUrl.contains(config.getAppId()), "URL应该包含AppID");
        assertTrue(accessTokenUrl.contains(config.getAppSecret()), "URL应该包含AppSecret");
        assertTrue(accessTokenUrl.contains("grant_type=client_credential"), "URL应该包含客户端凭证类型");
        
        System.out.println("构建的access_token URL: " + accessTokenUrl);
    }
    
    /**
     * 测试构建手机号URL
     */
    @Test
    public void testBuildPhoneNumberUrl() {
        String testAccessToken = "test_access_token_123";
        String phoneNumberUrl = config.buildPhoneNumberUrl(testAccessToken);
        
        assertNotNull(phoneNumberUrl, "手机号URL不应该为空");
        assertTrue(phoneNumberUrl.contains(testAccessToken), "URL应该包含access_token");
        assertTrue(phoneNumberUrl.contains("getuserphonenumber"), "URL应该包含获取手机号的路径");
        
        System.out.println("构建的手机号URL: " + phoneNumberUrl);
    }
    
    /**
     * 测试getter和setter方法
     */
    @Test
    public void testGettersAndSetters() {
        String testAppId = "test_app_id";
        String testAppSecret = "test_app_secret";
        String testLoginUrl = "https://test.login.url";
        
        config.setAppId(testAppId);
        config.setAppSecret(testAppSecret);
        config.setLoginUrl(testLoginUrl);
        
        assertEquals(testAppId, config.getAppId(), "AppID应该正确设置");
        assertEquals(testAppSecret, config.getAppSecret(), "AppSecret应该正确设置");
        assertEquals(testLoginUrl, config.getLoginUrl(), "登录URL应该正确设置");
        
        System.out.println("getter和setter方法测试通过");
    }
    
    /**
     * 测试完整的小程序登录流程URL构建
     */
    @Test
    public void testCompleteLoginFlow() {
        // 1. 构建小程序登录URL
        String jsCode = "mock_js_code_from_wx_login";
        String loginUrl = config.buildLoginUrl(jsCode);
        
        // 验证登录URL格式
        String expectedLoginUrl = String.format(
            "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
            config.getAppId(), config.getAppSecret(), jsCode
        );
        assertEquals(expectedLoginUrl, loginUrl, "登录URL应该符合预期格式");
        
        // 2. 构建获取全局access_token的URL
        String accessTokenUrl = config.buildAccessTokenUrl();
        String expectedAccessTokenUrl = String.format(
            "https://api.weixin.qq.com/cgi-bin/token?appid=%s&secret=%s&grant_type=client_credential",
            config.getAppId(), config.getAppSecret()
        );
        assertEquals(expectedAccessTokenUrl, accessTokenUrl, "access_token URL应该符合预期格式");
        
        // 3. 构建获取手机号的URL
        String accessToken = "mock_access_token";
        String phoneNumberUrl = config.buildPhoneNumberUrl(accessToken);
        String expectedPhoneNumberUrl = String.format(
            "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s",
            accessToken
        );
        assertEquals(expectedPhoneNumberUrl, phoneNumberUrl, "手机号URL应该符合预期格式");
        
        System.out.println("完整登录流程URL构建测试通过");
        System.out.println("1. 登录URL: " + loginUrl);
        System.out.println("2. 全局token URL: " + accessTokenUrl);
        System.out.println("3. 手机号URL: " + phoneNumberUrl);
    }
}
