package com.xnlpgyl.gift.Gift.weixin.entity;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 微信用户实体测试类
 * 
 * <AUTHOR>
 * @since 2025-06-29
 */
public class WeixinUserTest {
    
    private WeixinUser weixinUser;
    
    @BeforeEach
    public void setUp() {
        weixinUser = new WeixinUser();
    }
    
    /**
     * 测试默认构造函数
     */
    @Test
    public void testDefaultConstructor() {
        assertNotNull(weixinUser.getCreateTime(), "创建时间应该有默认值");
        assertNotNull(weixinUser.getUpdateTime(), "更新时间应该有默认值");
        assertEquals(0, weixinUser.getLoginCount(), "登录次数默认应该为0");
        assertTrue(weixinUser.getEnabled(), "用户默认应该是启用状态");
        
        System.out.println("默认构造函数测试通过");
        System.out.println("默认创建时间: " + weixinUser.getCreateTime());
        System.out.println("默认更新时间: " + weixinUser.getUpdateTime());
        System.out.println("默认登录次数: " + weixinUser.getLoginCount());
        System.out.println("默认启用状态: " + weixinUser.getEnabled());
    }
    
    /**
     * 测试性别描述功能
     */
    @Test
    public void testGetSexDesc() {
        // 测试男性
        weixinUser.setSex(1);
        assertEquals("男", weixinUser.getSexDesc(), "性别1应该对应男");
        
        // 测试女性
        weixinUser.setSex(2);
        assertEquals("女", weixinUser.getSexDesc(), "性别2应该对应女");
        
        // 测试未知
        weixinUser.setSex(0);
        assertEquals("未知", weixinUser.getSexDesc(), "性别0应该对应未知");
        
        // 测试其他值
        weixinUser.setSex(3);
        assertEquals("未知", weixinUser.getSexDesc(), "性别3应该对应未知");
        
        // 测试null值
        weixinUser.setSex(null);
        assertEquals("未知", weixinUser.getSexDesc(), "性别null应该对应未知");
        
        System.out.println("性别描述功能测试通过");
    }
    
    /**
     * 测试更新登录信息功能
     */
    @Test
    public void testUpdateLoginInfo() {
        // 记录更新前的状态
        LocalDateTime beforeUpdateTime = weixinUser.getUpdateTime();
        LocalDateTime beforeLoginTime = weixinUser.getLastLoginTime();
        Integer beforeLoginCount = weixinUser.getLoginCount();
        
        // 等待一毫秒确保时间不同
        try {
            Thread.sleep(1);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 执行更新登录信息
        weixinUser.updateLoginInfo();
        
        // 验证更新结果
        assertNotNull(weixinUser.getLastLoginTime(), "最后登录时间应该被设置");
        assertNotNull(weixinUser.getUpdateTime(), "更新时间应该被设置");
        assertEquals(beforeLoginCount + 1, weixinUser.getLoginCount(), "登录次数应该增加1");
        
        // 验证时间确实更新了
        assertTrue(weixinUser.getUpdateTime().isAfter(beforeUpdateTime) || 
                  weixinUser.getUpdateTime().isEqual(beforeUpdateTime), 
                  "更新时间应该不早于之前的时间");
        
        System.out.println("更新登录信息功能测试通过");
        System.out.println("更新前登录次数: " + beforeLoginCount);
        System.out.println("更新后登录次数: " + weixinUser.getLoginCount());
        System.out.println("最后登录时间: " + weixinUser.getLastLoginTime());
    }
    
    /**
     * 测试多次更新登录信息
     */
    @Test
    public void testMultipleUpdateLoginInfo() {
        // 初始状态
        assertEquals(0, weixinUser.getLoginCount(), "初始登录次数应该为0");
        
        // 第一次更新
        weixinUser.updateLoginInfo();
        assertEquals(1, weixinUser.getLoginCount(), "第一次更新后登录次数应该为1");
        
        // 第二次更新
        weixinUser.updateLoginInfo();
        assertEquals(2, weixinUser.getLoginCount(), "第二次更新后登录次数应该为2");
        
        // 第三次更新
        weixinUser.updateLoginInfo();
        assertEquals(3, weixinUser.getLoginCount(), "第三次更新后登录次数应该为3");
        
        System.out.println("多次更新登录信息测试通过");
        System.out.println("最终登录次数: " + weixinUser.getLoginCount());
    }
    
    /**
     * 测试所有属性的getter和setter
     */
    @Test
    public void testGettersAndSetters() {
        // 测试OpenID
        String testOpenId = "test_openid_123";
        weixinUser.setOpenId(testOpenId);
        assertEquals(testOpenId, weixinUser.getOpenId(), "OpenID应该正确设置和获取");
        
        // 测试昵称
        String testNickname = "测试用户";
        weixinUser.setNickname(testNickname);
        assertEquals(testNickname, weixinUser.getNickname(), "昵称应该正确设置和获取");
        
        // 测试性别
        Integer testSex = 1;
        weixinUser.setSex(testSex);
        assertEquals(testSex, weixinUser.getSex(), "性别应该正确设置和获取");
        
        // 测试省份
        String testProvince = "广东省";
        weixinUser.setProvince(testProvince);
        assertEquals(testProvince, weixinUser.getProvince(), "省份应该正确设置和获取");
        
        // 测试城市
        String testCity = "深圳市";
        weixinUser.setCity(testCity);
        assertEquals(testCity, weixinUser.getCity(), "城市应该正确设置和获取");
        
        // 测试国家
        String testCountry = "中国";
        weixinUser.setCountry(testCountry);
        assertEquals(testCountry, weixinUser.getCountry(), "国家应该正确设置和获取");
        
        // 测试头像URL
        String testHeadImgUrl = "http://example.com/avatar.jpg";
        weixinUser.setHeadImgUrl(testHeadImgUrl);
        assertEquals(testHeadImgUrl, weixinUser.getHeadImgUrl(), "头像URL应该正确设置和获取");
        
        // 测试UnionID
        String testUnionId = "test_unionid_123";
        weixinUser.setUnionId(testUnionId);
        assertEquals(testUnionId, weixinUser.getUnionId(), "UnionID应该正确设置和获取");
        
        // 测试访问令牌
        String testAccessToken = "test_access_token";
        weixinUser.setAccessToken(testAccessToken);
        assertEquals(testAccessToken, weixinUser.getAccessToken(), "访问令牌应该正确设置和获取");
        
        // 测试刷新令牌
        String testRefreshToken = "test_refresh_token";
        weixinUser.setRefreshToken(testRefreshToken);
        assertEquals(testRefreshToken, weixinUser.getRefreshToken(), "刷新令牌应该正确设置和获取");
        
        // 测试过期时间
        Integer testExpiresIn = 7200;
        weixinUser.setExpiresIn(testExpiresIn);
        assertEquals(testExpiresIn, weixinUser.getExpiresIn(), "过期时间应该正确设置和获取");
        
        // 测试授权范围
        String testScope = "snsapi_userinfo";
        weixinUser.setScope(testScope);
        assertEquals(testScope, weixinUser.getScope(), "授权范围应该正确设置和获取");
        
        // 测试特权信息
        String[] testPrivilege = {"chinaunicom", "test_privilege"};
        weixinUser.setPrivilege(testPrivilege);
        assertArrayEquals(testPrivilege, weixinUser.getPrivilege(), "特权信息应该正确设置和获取");
        
        // 测试启用状态
        Boolean testEnabled = false;
        weixinUser.setEnabled(testEnabled);
        assertEquals(testEnabled, weixinUser.getEnabled(), "启用状态应该正确设置和获取");
        
        System.out.println("所有getter和setter方法测试通过");
    }
    
    /**
     * 测试时间相关属性
     */
    @Test
    public void testTimeProperties() {
        LocalDateTime testCreateTime = LocalDateTime.of(2025, 6, 29, 10, 30, 0);
        LocalDateTime testUpdateTime = LocalDateTime.of(2025, 6, 29, 11, 30, 0);
        LocalDateTime testLastLoginTime = LocalDateTime.of(2025, 6, 29, 12, 30, 0);
        
        weixinUser.setCreateTime(testCreateTime);
        weixinUser.setUpdateTime(testUpdateTime);
        weixinUser.setLastLoginTime(testLastLoginTime);
        
        assertEquals(testCreateTime, weixinUser.getCreateTime(), "创建时间应该正确设置和获取");
        assertEquals(testUpdateTime, weixinUser.getUpdateTime(), "更新时间应该正确设置和获取");
        assertEquals(testLastLoginTime, weixinUser.getLastLoginTime(), "最后登录时间应该正确设置和获取");
        
        System.out.println("时间相关属性测试通过");
        System.out.println("创建时间: " + weixinUser.getCreateTime());
        System.out.println("更新时间: " + weixinUser.getUpdateTime());
        System.out.println("最后登录时间: " + weixinUser.getLastLoginTime());
    }
    
    /**
     * 测试toString方法
     */
    @Test
    public void testToString() {
        // 设置一些测试数据
        weixinUser.setOpenId("test_openid");
        weixinUser.setNickname("测试用户");
        weixinUser.setSex(1);
        weixinUser.setProvince("广东省");
        weixinUser.setCity("深圳市");
        weixinUser.setCountry("中国");
        weixinUser.setLoginCount(5);
        
        String toStringResult = weixinUser.toString();
        
        assertNotNull(toStringResult, "toString结果不应该为空");
        assertTrue(toStringResult.contains("test_openid"), "toString应该包含OpenID");
        assertTrue(toStringResult.contains("测试用户"), "toString应该包含昵称");
        assertTrue(toStringResult.contains("广东省"), "toString应该包含省份");
        assertTrue(toStringResult.contains("深圳市"), "toString应该包含城市");
        assertTrue(toStringResult.contains("中国"), "toString应该包含国家");
        
        System.out.println("toString方法测试通过");
        System.out.println("toString结果: " + toStringResult);
    }
    
    /**
     * 测试完整的用户信息设置
     */
    @Test
    public void testCompleteUserInfo() {
        // 设置完整的用户信息
        weixinUser.setOpenId("oxxxxxxxxxxxxxxxxxxxxxx");
        weixinUser.setNickname("微信用户");
        weixinUser.setSex(1);
        weixinUser.setProvince("广东省");
        weixinUser.setCity("深圳市");
        weixinUser.setCountry("中国");
        weixinUser.setHeadImgUrl("http://thirdwx.qlogo.cn/mmopen/test.jpg");
        weixinUser.setUnionId("unionid_test_123");
        weixinUser.setAccessToken("access_token_test");
        weixinUser.setRefreshToken("refresh_token_test");
        weixinUser.setExpiresIn(7200);
        weixinUser.setScope("snsapi_userinfo");
        weixinUser.setPrivilege(new String[]{"chinaunicom"});
        
        // 验证所有信息都正确设置
        assertEquals("oxxxxxxxxxxxxxxxxxxxxxx", weixinUser.getOpenId());
        assertEquals("微信用户", weixinUser.getNickname());
        assertEquals(1, weixinUser.getSex());
        assertEquals("男", weixinUser.getSexDesc());
        assertEquals("广东省", weixinUser.getProvince());
        assertEquals("深圳市", weixinUser.getCity());
        assertEquals("中国", weixinUser.getCountry());
        assertEquals("http://thirdwx.qlogo.cn/mmopen/test.jpg", weixinUser.getHeadImgUrl());
        assertEquals("unionid_test_123", weixinUser.getUnionId());
        assertEquals("access_token_test", weixinUser.getAccessToken());
        assertEquals("refresh_token_test", weixinUser.getRefreshToken());
        assertEquals(7200, weixinUser.getExpiresIn());
        assertEquals("snsapi_userinfo", weixinUser.getScope());
        assertArrayEquals(new String[]{"chinaunicom"}, weixinUser.getPrivilege());
        
        System.out.println("完整用户信息设置测试通过");
        System.out.println("用户信息: " + weixinUser.toString());
    }
}
