package com.xnlpgyl.gift.Gift.weixin.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 微信OAuth2.0配置测试类（不依赖Spring容器）
 * 
 * <AUTHOR>
 * @since 2025-06-29
 */
public class WeixinOAuth2ConfigTest {
    
    private WeixinOAuth2Config config;
    
    @BeforeEach
    public void setUp() {
        config = new WeixinOAuth2Config();
        // 设置测试配置
        config.setAppId("wx520c15f417810387");
        config.setAppSecret("d4624c36b6795d1d99dcf0547af5443d");
        config.setRedirectUri("https://www.xnlpgyl.com/api/gift/weixin/oauth2/callback");
        config.setScope("snsapi_userinfo");
        config.setState("XNLPGYL_WEIXIN_STATE");
    }
    
    /**
     * 测试构建授权URL
     */
    @Test
    public void testBuildAuthorizeUrl() {
        String authorizeUrl = config.buildAuthorizeUrl();
        
        assertNotNull(authorizeUrl, "授权URL不应该为空");
        assertTrue(authorizeUrl.contains(config.getAppId()), "授权URL应该包含AppID");
        assertTrue(authorizeUrl.contains(config.getRedirectUri()), "授权URL应该包含回调地址");
        assertTrue(authorizeUrl.contains(config.getScope()), "授权URL应该包含授权范围");
        assertTrue(authorizeUrl.contains(config.getState()), "授权URL应该包含状态参数");
        assertTrue(authorizeUrl.contains("response_type=code"), "授权URL应该包含响应类型");
        assertTrue(authorizeUrl.endsWith("#wechat_redirect"), "授权URL应该以#wechat_redirect结尾");
        
        System.out.println("构建的授权URL: " + authorizeUrl);
    }
    
    /**
     * 测试构建获取access_token的URL
     */
    @Test
    public void testBuildAccessTokenUrl() {
        String testCode = "test_code_123456";
        String accessTokenUrl = config.buildAccessTokenUrl(testCode);
        
        assertNotNull(accessTokenUrl, "获取access_token的URL不应该为空");
        assertTrue(accessTokenUrl.contains(config.getAppId()), "URL应该包含AppID");
        assertTrue(accessTokenUrl.contains(config.getAppSecret()), "URL应该包含AppSecret");
        assertTrue(accessTokenUrl.contains(testCode), "URL应该包含授权码");
        assertTrue(accessTokenUrl.contains("grant_type=authorization_code"), "URL应该包含授权类型");
        
        System.out.println("构建的access_token URL: " + accessTokenUrl);
    }
    
    /**
     * 测试构建获取用户信息的URL
     */
    @Test
    public void testBuildUserInfoUrl() {
        String testAccessToken = "test_access_token_123456";
        String testOpenId = "test_openid_123456";
        String userInfoUrl = config.buildUserInfoUrl(testAccessToken, testOpenId);
        
        assertNotNull(userInfoUrl, "获取用户信息的URL不应该为空");
        assertTrue(userInfoUrl.contains(testAccessToken), "URL应该包含访问令牌");
        assertTrue(userInfoUrl.contains(testOpenId), "URL应该包含OpenID");
        assertTrue(userInfoUrl.contains("lang=zh_CN"), "URL应该包含语言参数");
        
        System.out.println("构建的用户信息URL: " + userInfoUrl);
    }
    
    /**
     * 测试构建刷新token的URL
     */
    @Test
    public void testBuildRefreshTokenUrl() {
        String testRefreshToken = "test_refresh_token_123456";
        String refreshTokenUrl = config.buildRefreshTokenUrl(testRefreshToken);
        
        assertNotNull(refreshTokenUrl, "刷新token的URL不应该为空");
        assertTrue(refreshTokenUrl.contains(config.getAppId()), "URL应该包含AppID");
        assertTrue(refreshTokenUrl.contains(testRefreshToken), "URL应该包含刷新令牌");
        assertTrue(refreshTokenUrl.contains("grant_type=refresh_token"), "URL应该包含授权类型");
        
        System.out.println("构建的刷新token URL: " + refreshTokenUrl);
    }
    
    /**
     * 测试构建验证token的URL
     */
    @Test
    public void testBuildValidateTokenUrl() {
        String testAccessToken = "test_access_token_123456";
        String testOpenId = "test_openid_123456";
        String validateTokenUrl = config.buildValidateTokenUrl(testAccessToken, testOpenId);
        
        assertNotNull(validateTokenUrl, "验证token的URL不应该为空");
        assertTrue(validateTokenUrl.contains(testAccessToken), "URL应该包含访问令牌");
        assertTrue(validateTokenUrl.contains(testOpenId), "URL应该包含OpenID");
        
        System.out.println("构建的验证token URL: " + validateTokenUrl);
    }
    
    /**
     * 测试配置的getter和setter方法
     */
    @Test
    public void testGettersAndSetters() {
        // 测试AppID
        String testAppId = "test_app_id";
        config.setAppId(testAppId);
        assertEquals(testAppId, config.getAppId(), "AppID应该正确设置和获取");
        
        // 测试AppSecret
        String testAppSecret = "test_app_secret";
        config.setAppSecret(testAppSecret);
        assertEquals(testAppSecret, config.getAppSecret(), "AppSecret应该正确设置和获取");
        
        // 测试授权URL
        String testAuthorizeUrl = "https://test.authorize.url";
        config.setAuthorizeUrl(testAuthorizeUrl);
        assertEquals(testAuthorizeUrl, config.getAuthorizeUrl(), "授权URL应该正确设置和获取");
        
        // 测试回调地址
        String testRedirectUri = "https://test.redirect.uri";
        config.setRedirectUri(testRedirectUri);
        assertEquals(testRedirectUri, config.getRedirectUri(), "回调地址应该正确设置和获取");
        
        // 测试授权范围
        String testScope = "test_scope";
        config.setScope(testScope);
        assertEquals(testScope, config.getScope(), "授权范围应该正确设置和获取");
        
        // 测试状态参数
        String testState = "test_state";
        config.setState(testState);
        assertEquals(testState, config.getState(), "状态参数应该正确设置和获取");
        
        System.out.println("所有getter和setter方法测试通过");
    }
    
    /**
     * 测试默认配置值
     */
    @Test
    public void testDefaultValues() {
        WeixinOAuth2Config defaultConfig = new WeixinOAuth2Config();
        
        assertNotNull(defaultConfig.getAppId(), "默认AppID不应该为空");
        assertNotNull(defaultConfig.getAppSecret(), "默认AppSecret不应该为空");
        assertNotNull(defaultConfig.getAuthorizeUrl(), "默认授权URL不应该为空");
        assertNotNull(defaultConfig.getAccessTokenUrl(), "默认获取token URL不应该为空");
        assertNotNull(defaultConfig.getUserInfoUrl(), "默认获取用户信息URL不应该为空");
        assertNotNull(defaultConfig.getRefreshTokenUrl(), "默认刷新token URL不应该为空");
        assertNotNull(defaultConfig.getValidateTokenUrl(), "默认验证token URL不应该为空");
        assertNotNull(defaultConfig.getRedirectUri(), "默认回调地址不应该为空");
        assertNotNull(defaultConfig.getScope(), "默认授权范围不应该为空");
        assertNotNull(defaultConfig.getState(), "默认状态参数不应该为空");
        
        // 验证默认值
        assertEquals("wx520c15f417810387", defaultConfig.getAppId(), "默认AppID应该正确");
        assertEquals("snsapi_userinfo", defaultConfig.getScope(), "默认授权范围应该是snsapi_userinfo");
        assertEquals("STATE", defaultConfig.getState(), "默认状态参数应该是STATE");
        
        System.out.println("默认配置值验证通过");
        System.out.println("默认AppID: " + defaultConfig.getAppId());
        System.out.println("默认授权范围: " + defaultConfig.getScope());
        System.out.println("默认状态参数: " + defaultConfig.getState());
        System.out.println("默认回调地址: " + defaultConfig.getRedirectUri());
    }
    
    /**
     * 测试URL构建的完整性
     */
    @Test
    public void testUrlBuildingIntegrity() {
        // 测试完整的OAuth2.0流程URL构建
        
        // 1. 构建授权URL
        String authorizeUrl = config.buildAuthorizeUrl();
        assertTrue(authorizeUrl.startsWith("https://open.weixin.qq.com/connect/oauth2/authorize"), 
                "授权URL应该以正确的微信授权地址开始");
        
        // 2. 模拟获取授权码后构建access_token URL
        String code = "mock_authorization_code";
        String accessTokenUrl = config.buildAccessTokenUrl(code);
        assertTrue(accessTokenUrl.startsWith("https://api.weixin.qq.com/sns/oauth2/access_token"), 
                "获取token URL应该以正确的微信API地址开始");
        
        // 3. 模拟获取access_token后构建用户信息URL
        String accessToken = "mock_access_token";
        String openId = "mock_openid";
        String userInfoUrl = config.buildUserInfoUrl(accessToken, openId);
        assertTrue(userInfoUrl.startsWith("https://api.weixin.qq.com/sns/userinfo"), 
                "获取用户信息URL应该以正确的微信API地址开始");
        
        // 4. 构建刷新token URL
        String refreshToken = "mock_refresh_token";
        String refreshTokenUrl = config.buildRefreshTokenUrl(refreshToken);
        assertTrue(refreshTokenUrl.startsWith("https://api.weixin.qq.com/sns/oauth2/refresh_token"), 
                "刷新token URL应该以正确的微信API地址开始");
        
        // 5. 构建验证token URL
        String validateTokenUrl = config.buildValidateTokenUrl(accessToken, openId);
        assertTrue(validateTokenUrl.startsWith("https://api.weixin.qq.com/sns/auth"), 
                "验证token URL应该以正确的微信API地址开始");
        
        System.out.println("URL构建完整性测试通过");
        System.out.println("授权URL: " + authorizeUrl);
        System.out.println("获取token URL: " + accessTokenUrl);
        System.out.println("获取用户信息URL: " + userInfoUrl);
        System.out.println("刷新token URL: " + refreshTokenUrl);
        System.out.println("验证token URL: " + validateTokenUrl);
    }
}
