package com.xnlpgyl.gift.Gift.User;

import com.xnlpgyl.gift.Gift.User.service.impl.UserServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 验证码失败限制测试
 */
public class CaptchaFailLimitTest {

    @BeforeEach
    void setUp() {
        // 清理测试数据
        UserServiceImpl.captchaFailMap.clear();
        UserServiceImpl.captchaErrorTimeMap.clear();
    }

    @Test
    void testCaptchaFailLimit() {
        String username = "testuser";
        
        // 初始状态：未超过限制
        assertFalse(UserServiceImpl.isCaptchaFailExceedLimit(username));
        assertEquals(0, UserServiceImpl.getCaptchaFailCount(username));
        
        // 记录4次失败，未超过限制
        for (int i = 1; i <= 4; i++) {
            UserServiceImpl.recordCaptchaFail(username);
            assertEquals(i, UserServiceImpl.getCaptchaFailCount(username));
            assertFalse(UserServiceImpl.isCaptchaFailExceedLimit(username));
        }
        
        // 第5次失败，达到限制
        UserServiceImpl.recordCaptchaFail(username);
        assertEquals(5, UserServiceImpl.getCaptchaFailCount(username));
        assertTrue(UserServiceImpl.isCaptchaFailExceedLimit(username));
        
        // 继续记录失败，仍然超过限制
        UserServiceImpl.recordCaptchaFail(username);
        assertEquals(6, UserServiceImpl.getCaptchaFailCount(username));
        assertTrue(UserServiceImpl.isCaptchaFailExceedLimit(username));
    }

    @Test
    void testClearCaptchaFailRecord() {
        String username = "testuser";
        
        // 记录5次失败
        for (int i = 0; i < 5; i++) {
            UserServiceImpl.recordCaptchaFail(username);
        }
        
        // 验证已超过限制
        assertTrue(UserServiceImpl.isCaptchaFailExceedLimit(username));
        assertEquals(5, UserServiceImpl.getCaptchaFailCount(username));
        
        // 清除记录
        UserServiceImpl.clearCaptchaFailRecord(username);
        
        // 验证已清除
        assertFalse(UserServiceImpl.isCaptchaFailExceedLimit(username));
        assertEquals(0, UserServiceImpl.getCaptchaFailCount(username));
    }

    @Test
    void testMultipleUsers() {
        String user1 = "user1";
        String user2 = "user2";
        
        // user1 记录3次失败
        for (int i = 0; i < 3; i++) {
            UserServiceImpl.recordCaptchaFail(user1);
        }
        
        // user2 记录5次失败
        for (int i = 0; i < 5; i++) {
            UserServiceImpl.recordCaptchaFail(user2);
        }
        
        // 验证不同用户的状态
        assertEquals(3, UserServiceImpl.getCaptchaFailCount(user1));
        assertFalse(UserServiceImpl.isCaptchaFailExceedLimit(user1));
        
        assertEquals(5, UserServiceImpl.getCaptchaFailCount(user2));
        assertTrue(UserServiceImpl.isCaptchaFailExceedLimit(user2));
        
        // 清除user1的记录，不影响user2
        UserServiceImpl.clearCaptchaFailRecord(user1);
        
        assertEquals(0, UserServiceImpl.getCaptchaFailCount(user1));
        assertFalse(UserServiceImpl.isCaptchaFailExceedLimit(user1));
        
        assertEquals(5, UserServiceImpl.getCaptchaFailCount(user2));
        assertTrue(UserServiceImpl.isCaptchaFailExceedLimit(user2));
    }
}
