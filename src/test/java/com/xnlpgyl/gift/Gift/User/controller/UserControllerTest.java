package com.xnlpgyl.gift.Gift.User.controller;

import com.xnlpgyl.gift.Gift.User.dto.UpdateNicknameRequest;
import com.xnlpgyl.gift.Gift.User.dto.UpdatePasswordRequest;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户控制器测试类
 * 测试新增的修改昵称和密码接口
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */
public class UserControllerTest {
    
    /**
     * 测试UpdateNicknameRequest DTO
     */
    @Test
    public void testUpdateNicknameRequest() {
        UpdateNicknameRequest request = new UpdateNicknameRequest();
        request.setUsername("testuser");
        request.setNickname("新昵称");

        assertEquals("testuser", request.getUsername());
        assertEquals("新昵称", request.getNickname());

        UpdateNicknameRequest request2 = new UpdateNicknameRequest("user123", "测试昵称");
        assertEquals("user123", request2.getUsername());
        assertEquals("测试昵称", request2.getNickname());

        String toString = request.toString();
        assertTrue(toString.contains("testuser"));
        assertTrue(toString.contains("新昵称"));

        System.out.println("UpdateNicknameRequest测试通过");
    }
    
    /**
     * 测试UpdatePasswordRequest DTO
     */
    @Test
    public void testUpdatePasswordRequest() {
        UpdatePasswordRequest request = new UpdatePasswordRequest();
        request.setUsername("testuser");
        request.setCurrentPassword("oldPassword");
        request.setNewPassword("newPassword");
        request.setConfirmPassword("newPassword");

        assertEquals("testuser", request.getUsername());
        assertEquals("oldPassword", request.getCurrentPassword());
        assertEquals("newPassword", request.getNewPassword());
        assertEquals("newPassword", request.getConfirmPassword());

        UpdatePasswordRequest request2 = new UpdatePasswordRequest("user123", "old", "new", "new");
        assertEquals("user123", request2.getUsername());
        assertEquals("old", request2.getCurrentPassword());
        assertEquals("new", request2.getNewPassword());
        assertEquals("new", request2.getConfirmPassword());

        String toString = request.toString();
        assertTrue(toString.contains("testuser"));
        assertTrue(toString.contains("[PROTECTED]"));
        // 注意：toString方法确实保护了密码，不会显示实际密码内容

        System.out.println("UpdatePasswordRequest测试通过");
    }
    
    /**
     * 测试DTO验证注解
     */
    @Test
    public void testDTOValidation() {
        // 测试昵称请求的验证
        UpdateNicknameRequest nicknameRequest = new UpdateNicknameRequest();
        nicknameRequest.setUsername("");  // 空用户名应该在实际验证中失败
        nicknameRequest.setNickname("");  // 空昵称应该在实际验证中失败
        assertNotNull(nicknameRequest);

        // 测试密码请求的验证
        UpdatePasswordRequest passwordRequest = new UpdatePasswordRequest();
        passwordRequest.setUsername("");  // 空用户名应该在实际验证中失败
        passwordRequest.setCurrentPassword("");  // 空密码应该在实际验证中失败
        passwordRequest.setNewPassword("");
        passwordRequest.setConfirmPassword("");
        assertNotNull(passwordRequest);

        System.out.println("DTO验证注解测试通过");
    }
    
    /**
     * 测试密码长度验证
     */
    @Test
    public void testPasswordLength() {
        UpdatePasswordRequest request = new UpdatePasswordRequest();
        
        // 测试短密码
        request.setNewPassword("123");
        assertEquals("123", request.getNewPassword());
        
        // 测试长密码
        request.setNewPassword("123456789012345678901234567890");
        assertEquals("123456789012345678901234567890", request.getNewPassword());
        
        // 测试正常长度密码
        request.setNewPassword("123456");
        assertEquals("123456", request.getNewPassword());
        
        System.out.println("密码长度测试通过");
    }
    
    /**
     * 测试昵称长度验证
     */
    @Test
    public void testNicknameLength() {
        UpdateNicknameRequest request = new UpdateNicknameRequest();
        
        // 测试短昵称
        request.setNickname("A");
        assertEquals("A", request.getNickname());
        
        // 测试长昵称
        String longNickname = "这是一个非常长的昵称测试字符串，用来测试昵称长度限制功能是否正常工作";
        request.setNickname(longNickname);
        assertEquals(longNickname, request.getNickname());
        
        // 测试正常长度昵称
        request.setNickname("正常昵称");
        assertEquals("正常昵称", request.getNickname());
        
        System.out.println("昵称长度测试通过");
    }
    
    /**
     * 测试密码确认逻辑
     */
    @Test
    public void testPasswordConfirmation() {
        UpdatePasswordRequest request = new UpdatePasswordRequest();
        
        // 测试密码一致
        request.setNewPassword("password123");
        request.setConfirmPassword("password123");
        assertEquals(request.getNewPassword(), request.getConfirmPassword());
        
        // 测试密码不一致
        request.setNewPassword("password123");
        request.setConfirmPassword("password456");
        assertNotEquals(request.getNewPassword(), request.getConfirmPassword());
        
        System.out.println("密码确认逻辑测试通过");
    }
    
    /**
     * 测试DTO的完整性
     */
    @Test
    public void testDTOCompleteness() {
        // 测试UpdateNicknameRequest的完整性
        UpdateNicknameRequest nicknameRequest = new UpdateNicknameRequest("testuser", "测试昵称");
        assertNotNull(nicknameRequest.getUsername());
        assertNotNull(nicknameRequest.getNickname());
        assertNotNull(nicknameRequest.toString());

        // 测试UpdatePasswordRequest的完整性
        UpdatePasswordRequest passwordRequest = new UpdatePasswordRequest("testuser", "old", "new", "confirm");
        assertNotNull(passwordRequest.getUsername());
        assertNotNull(passwordRequest.getCurrentPassword());
        assertNotNull(passwordRequest.getNewPassword());
        assertNotNull(passwordRequest.getConfirmPassword());
        assertNotNull(passwordRequest.toString());

        System.out.println("DTO完整性测试通过");
    }
    
    /**
     * 测试边界情况
     */
    @Test
    public void testBoundaryConditions() {
        // 测试null值处理
        UpdateNicknameRequest nicknameRequest = new UpdateNicknameRequest();
        nicknameRequest.setUsername(null);
        nicknameRequest.setNickname(null);
        assertNull(nicknameRequest.getUsername());
        assertNull(nicknameRequest.getNickname());

        UpdatePasswordRequest passwordRequest = new UpdatePasswordRequest();
        passwordRequest.setUsername(null);
        passwordRequest.setCurrentPassword(null);
        passwordRequest.setNewPassword(null);
        passwordRequest.setConfirmPassword(null);
        assertNull(passwordRequest.getUsername());
        assertNull(passwordRequest.getCurrentPassword());
        assertNull(passwordRequest.getNewPassword());
        assertNull(passwordRequest.getConfirmPassword());

        // 测试空字符串
        nicknameRequest.setUsername("");
        nicknameRequest.setNickname("");
        assertEquals("", nicknameRequest.getUsername());
        assertEquals("", nicknameRequest.getNickname());

        passwordRequest.setUsername("");
        passwordRequest.setCurrentPassword("");
        passwordRequest.setNewPassword("");
        passwordRequest.setConfirmPassword("");
        assertEquals("", passwordRequest.getUsername());
        assertEquals("", passwordRequest.getCurrentPassword());
        assertEquals("", passwordRequest.getNewPassword());
        assertEquals("", passwordRequest.getConfirmPassword());

        System.out.println("边界条件测试通过");
    }
    
    /**
     * 测试安全性
     */
    @Test
    public void testSecurity() {
        UpdatePasswordRequest request = new UpdatePasswordRequest("testuser", "sensitivePassword", "newSensitivePassword", "newSensitivePassword");

        // 确保toString方法不会泄露密码
        String toString = request.toString();
        assertTrue(toString.contains("testuser"));  // 用户名应该显示
        assertFalse(toString.contains("sensitivePassword"));
        assertFalse(toString.contains("newSensitivePassword"));
        assertTrue(toString.contains("[PROTECTED]"));

        System.out.println("安全性测试通过");
        System.out.println("密码在toString中被正确保护: " + toString);
    }
}
