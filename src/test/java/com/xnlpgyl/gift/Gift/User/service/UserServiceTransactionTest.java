package com.xnlpgyl.gift.Gift.User.service;

import com.xnlpgyl.gift.Gift.SysRole.entity.SysRole;
import com.xnlpgyl.gift.Gift.User.entity.User;
import com.xnlpgyl.gift.Gift.User.service.IUserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户服务事务管理测试类
 * 测试UserServiceImpl中各个方法的事务管理功能
 */
@SpringBootTest
@Transactional
@Rollback
public class UserServiceTransactionTest {

    @Autowired
    private IUserService userService;

    /**
     * 测试saveRole方法的事务管理
     * 验证在保存用户角色时，如果发生异常，事务能够正确回滚
     */
    @Test
    public void testSaveRoleTransaction() {
        // 创建测试用户
        User testUser = new User();
        testUser.setUsername("test_transaction_user");
        testUser.setPassword("test_password");
        testUser.setNickname("测试事务用户");
        testUser.setEmail("<EMAIL>");
        
        // 保存用户
        userService.save(testUser);
        assertNotNull(testUser.getId(), "用户应该被成功保存并获得ID");
        
        // 测试角色保存
        List<Long> roleIds = Arrays.asList(1L, 2L);
        
        // 这个方法应该在事务中执行
        assertDoesNotThrow(() -> {
            userService.saveRole(testUser.getId(), roleIds);
        }, "保存用户角色不应该抛出异常");
        
        // 验证角色是否正确保存
        List<SysRole> userRoles = userService.getUserRoles(testUser.getId());
        // 注意：由于测试环境可能没有实际的角色数据，这里主要验证方法调用不会出错
        assertNotNull(userRoles, "获取用户角色不应该返回null");
    }

    /**
     * 测试loadLoginUserByUsername方法的只读事务
     */
    @Test
    public void testLoadLoginUserByUsernameReadOnlyTransaction() {
        // 测试只读事务方法
        assertDoesNotThrow(() -> {
            userService.loadLoginUserByUsername("nonexistent_user");
        }, "加载不存在的用户不应该抛出异常");
    }

    /**
     * 测试getRolesByUserId方法的只读事务
     */
    @Test
    public void testGetRolesByUserIdReadOnlyTransaction() {
        // 测试只读事务方法
        assertDoesNotThrow(() -> {
            List<SysRole> roles = userService.getRolesByUserId(999L);
            // 对于不存在的用户ID，应该返回null或空列表
        }, "获取不存在用户的角色不应该抛出异常");
    }

    /**
     * 测试getUserRoles方法的只读事务
     */
    @Test
    public void testGetUserRolesReadOnlyTransaction() {
        // 测试只读事务方法
        assertDoesNotThrow(() -> {
            List<SysRole> roles = userService.getUserRoles(999L);
            // 对于不存在的用户ID，应该返回null或空列表
        }, "获取不存在用户的角色不应该抛出异常");
    }

    /**
     * 测试事务注解是否正确配置
     * 通过反射检查方法上的事务注解
     */
    @Test
    public void testTransactionAnnotationsPresent() throws NoSuchMethodException {
        Class<?> serviceClass = userService.getClass();
        
        // 检查saveRole方法的事务注解
        assertTrue(
            serviceClass.getMethod("saveRole", Long.class, List.class)
                .isAnnotationPresent(org.springframework.transaction.annotation.Transactional.class),
            "saveRole方法应该有@Transactional注解"
        );
        
        // 检查loadLoginUserByUsername方法的事务注解
        assertTrue(
            serviceClass.getMethod("loadLoginUserByUsername", String.class)
                .isAnnotationPresent(org.springframework.transaction.annotation.Transactional.class),
            "loadLoginUserByUsername方法应该有@Transactional注解"
        );
        
        // 检查getRolesByUserId方法的事务注解
        assertTrue(
            serviceClass.getMethod("getRolesByUserId", Long.class)
                .isAnnotationPresent(org.springframework.transaction.annotation.Transactional.class),
            "getRolesByUserId方法应该有@Transactional注解"
        );
        
        // 检查getUserRoles方法的事务注解
        assertTrue(
            serviceClass.getMethod("getUserRoles", Long.class)
                .isAnnotationPresent(org.springframework.transaction.annotation.Transactional.class),
            "getUserRoles方法应该有@Transactional注解"
        );
    }
}
