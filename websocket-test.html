<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .system { background-color: #e7f3ff; }
        .sent { background-color: #d4edda; }
        .received { background-color: #fff3cd; }
        .error { background-color: #f8d7da; }
        input, button, select {
            margin: 5px;
            padding: 8px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>WebSocket 服务测试</h1>
    
    <!-- 连接状态 -->
    <div class="container">
        <h3>连接状态</h3>
        <div id="status" class="status disconnected">未连接</div>
        <input type="text" id="userId" placeholder="输入用户ID" value="user123">
        <button id="connectBtn" onclick="connect()">连接</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
    </div>
    
    <!-- 消息显示区域 -->
    <div class="container">
        <h3>消息记录</h3>
        <div id="messages" class="messages"></div>
        <button onclick="clearMessages()">清空消息</button>
    </div>
    
    <!-- 发送消息 -->
    <div class="container">
        <h3>发送消息</h3>
        <div>
            <select id="messageType">
                <option value="PRIVATE_MESSAGE">私聊消息</option>
                <option value="BROADCAST_MESSAGE">广播消息</option>
                <option value="HEARTBEAT">心跳检测</option>
            </select>
        </div>
        <div>
            <input type="text" id="toUserId" placeholder="接收者用户ID (私聊时必填)" value="user456">
        </div>
        <div>
            <input type="text" id="messageContent" placeholder="消息内容" value="Hello WebSocket!">
        </div>
        <button onclick="sendMessage()" id="sendBtn" disabled>发送消息</button>
    </div>
    
    <!-- REST API 测试 -->
    <div class="container">
        <h3>REST API 测试</h3>
        <button onclick="getOnlineUsers()">获取在线用户</button>
        <button onclick="getStats()">获取连接统计</button>
        <button onclick="sendNotification()">发送系统通知</button>
    </div>

    <script>
        let websocket = null;
        let userId = '';

        function addMessage(content, type = 'system') {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${content}`;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function updateStatus(connected) {
            const status = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const sendBtn = document.getElementById('sendBtn');
            
            if (connected) {
                status.textContent = `已连接 (用户: ${userId})`;
                status.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendBtn.disabled = false;
            } else {
                status.textContent = '未连接';
                status.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
            }
        }

        function connect() {
            userId = document.getElementById('userId').value.trim();
            if (!userId) {
                alert('请输入用户ID');
                return;
            }

            try {
                websocket = new WebSocket(`ws://localhost:8080/websocket/${userId}`);
                
                websocket.onopen = function(event) {
                    addMessage('WebSocket连接已建立', 'system');
                    updateStatus(true);
                };
                
                websocket.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        addMessage(`收到消息: ${JSON.stringify(message, null, 2)}`, 'received');
                    } catch (e) {
                        addMessage(`收到消息: ${event.data}`, 'received');
                    }
                };
                
                websocket.onclose = function(event) {
                    addMessage(`WebSocket连接已关闭 (代码: ${event.code})`, 'system');
                    updateStatus(false);
                };
                
                websocket.onerror = function(error) {
                    addMessage(`WebSocket错误: ${error}`, 'error');
                };
                
            } catch (error) {
                addMessage(`连接失败: ${error}`, 'error');
            }
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }

        function sendMessage() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                alert('WebSocket未连接');
                return;
            }

            const messageType = document.getElementById('messageType').value;
            const toUserId = document.getElementById('toUserId').value.trim();
            const content = document.getElementById('messageContent').value.trim();

            if (!content) {
                alert('请输入消息内容');
                return;
            }

            if (messageType === 'PRIVATE_MESSAGE' && !toUserId) {
                alert('私聊消息需要指定接收者用户ID');
                return;
            }

            const message = {
                type: messageType,
                content: content
            };

            if (messageType === 'PRIVATE_MESSAGE') {
                message.toUserId = toUserId;
            }

            try {
                websocket.send(JSON.stringify(message));
                addMessage(`发送消息: ${JSON.stringify(message, null, 2)}`, 'sent');
            } catch (error) {
                addMessage(`发送失败: ${error}`, 'error');
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // REST API 测试函数
        async function getOnlineUsers() {
            try {
                const response = await fetch('/websocket/online-users');
                const result = await response.json();
                addMessage(`在线用户: ${JSON.stringify(result, null, 2)}`, 'system');
            } catch (error) {
                addMessage(`获取在线用户失败: ${error}`, 'error');
            }
        }

        async function getStats() {
            try {
                const response = await fetch('/websocket/stats');
                const result = await response.json();
                addMessage(`连接统计: ${JSON.stringify(result, null, 2)}`, 'system');
            } catch (error) {
                addMessage(`获取统计信息失败: ${error}`, 'error');
            }
        }

        async function sendNotification() {
            const content = prompt('请输入通知内容:', '系统维护通知');
            if (!content) return;

            try {
                const response = await fetch(`/websocket/notification?content=${encodeURIComponent(content)}`, {
                    method: 'POST'
                });
                const result = await response.json();
                addMessage(`发送通知结果: ${JSON.stringify(result, null, 2)}`, 'system');
            } catch (error) {
                addMessage(`发送通知失败: ${error}`, 'error');
            }
        }

        // 页面加载完成后初始化
        window.onload = function() {
            updateStatus(false);
            addMessage('WebSocket测试页面已加载，请输入用户ID并点击连接', 'system');
        };

        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            if (websocket) {
                websocket.close();
            }
        };
    </script>
</body>
</html>
